/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  agent_evaluation: () => import("@/views/agent/evaluation/index.vue"),
  agent_home: () => import("@/views/agent/home/<USER>"),
  agent_list: () => import("@/views/agent/list/index.vue"),
  agent_spoken: () => import("@/views/agent/spoken/index.vue"),
  agent_statistics: () => import("@/views/agent/statistics/index.vue"),
  manage_dict: () => import("@/views/manage/dict/index.vue"),
  manage_menu: () => import("@/views/manage/menu/index.vue"),
  manage_role: () => import("@/views/manage/role/index.vue"),
  "manage_user-detail": () => import("@/views/manage/user-detail/[id].vue"),
  manage_user: () => import("@/views/manage/user/index.vue"),
  questions: () => import("@/views/questions/index.vue"),
  "teaching-plan-records_edit": () => import("@/views/teaching-plan-records/edit/index.vue"),
  "teaching-plan-records_home": () => import("@/views/teaching-plan-records/home/<USER>"),
  "teaching-plan-records_test": () => import("@/views/teaching-plan-records/test/index.vue"),
  "teaching-plan_create": () => import("@/views/teaching-plan/create/index.vue"),
  "teaching-plan_home": () => import("@/views/teaching-plan/home/<USER>"),
  "teaching-plan_ppt-gen": () => import("@/views/teaching-plan/ppt-gen/index.vue"),
};
