<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import type { TransformToVoQuestionData } from '@sa/utils'
import CKEditor from '@sa/components/common/ck-editor/index.vue'
import LeftView from './components/left-view/index.vue'
import RightView from './components/right-view/index.vue'

defineOptions({
  name: 'Questions',
})

// 题目数据状态
const questionsList = ref<TransformToVoQuestionData[]>([])
const currentProgress = ref({ current: 0, total: 0, description: '' })
const isGenerating = ref(false)
const generationError = ref('')

// 左侧表单引用
const leftViewRef = ref()

// 获取左侧表单数据的计算属性
const formData = computed(() => leftViewRef.value?.formModel || {})
const aiModeId = computed(() => formData.value.AIModeId || '')
const difficultyId = computed(() => formData.value.DifficultyId || '')
const learningLevelId = computed(() => formData.value.LearningLevelId || '')
const chapterOptions = computed(() => leftViewRef.value?.chapterOptions || [])

// 处理题目生成事件
function handleQuestionGenerated(data: TransformToVoQuestionData) {
  questionsList.value.push(data)
}

function handleGenerationStarted() {
  isGenerating.value = true
  generationError.value = ''
}

function handleGenerationComplete() {
  isGenerating.value = false
  console.log('题目生成完成，共生成', questionsList.value.length, '道题目')
}

function handleGenerationError(error: string) {
  isGenerating.value = false
  generationError.value = error
  console.error('题目生成错误:', error)
}

// 处理删除题目事件
function handleDeleteQuestion(questionId: string) {
  const index = questionsList.value.findIndex((q: TransformToVoQuestionData) => q.id === questionId)
  if (index > -1) {
    questionsList.value.splice(index, 1)
  }
}

// 处理清空所有题目
function handleClearAll() {
  // 清空题目列表
  questionsList.value = []
}

// 处理插入题目事件
function handleInsertQuestion(question: TransformToVoQuestionData) {
  // 将新题目插入到题目列表的末尾
  questionsList.value.unshift(question)
  console.log('题目插入成功，当前题目总数:', questionsList.value.length)
}

// 处理替换题目事件
function handleReplaceQuestion(oldQuestionId: string, newQuestion: TransformToVoQuestionData) {
  const index = questionsList.value.findIndex((q: TransformToVoQuestionData) => q.id === oldQuestionId)
  if (index > -1) {
    // 替换指定位置的题目
    questionsList.value.splice(index, 1, newQuestion)
    console.log('题目替换成功，替换位置:', index + 1)
  }
  else {
    console.error('未找到要替换的题目:', oldQuestionId)
    window.$message?.error('未找到要替换的题目')
  }
}

// 处理题目更新事件
function handleUpdateQuestion(questionId: string, updatedQuestion: TransformToVoQuestionData) {
  const index = questionsList.value.findIndex((q: TransformToVoQuestionData) => q.id === questionId)
  if (index > -1) {
    // 更新指定位置的题目
    questionsList.value.splice(index, 1, updatedQuestion)
    console.log('题目更新成功，更新位置:', index + 1)
  }
  else {
    console.error('未找到要更新的题目:', questionId)
    window.$message?.error('未找到要更新的题目')
  }
}

// 刷新阻止事件处理
function handleBeforeUnload(event: BeforeUnloadEvent) {
  // 阻止默认行为，浏览器会显示确认对话框
  event.preventDefault()
  // 现代浏览器会显示标准的确认消息
  return ''
}

// 组件挂载时添加事件监听器
onMounted(() => {
  // window.addEventListener('beforeunload', handleBeforeUnload)
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
</script>

<template>
  <div class="bg-container-view relative h-full w-full rounded-8px bg-[#F2F6FC]">
    <!-- 背景 -->
    <div class="relative h-full flex rounded-8px">
      <LeftView
        ref="leftViewRef"
        class="mr-12px w-30%"
        @question-generated="handleQuestionGenerated"
        @generation-started="handleGenerationStarted"
        @generation-complete="handleGenerationComplete"
        @generation-error="handleGenerationError"
      />
      <div class="test w-500px">
        <CKEditor v-for="item in 10" :key="item" class="mb-20px" :min-height="200" />
      </div>
      <RightView
        class="h-full w-70%"
        :questions-list="questionsList"
        :current-progress="currentProgress"
        :is-generating="isGenerating"
        :generation-error="generationError"
        :ai-mode-id="aiModeId"
        :difficulty-id="difficultyId"
        :learning-level-id="learningLevelId"
        :form-data="formData"
        :chapter-options="chapterOptions"
        @delete-question="handleDeleteQuestion"
        @clear-all="handleClearAll"
        @insert-question="handleInsertQuestion"
        @replace-question="handleReplaceQuestion"
        @update-question="handleUpdateQuestion"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.bg-container-view{
  background: url(https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGa2cc791875d5eb4639bbba2971f330fc.png) 100% no-repeat;
  background-size: 100% 100%;
}
</style>
