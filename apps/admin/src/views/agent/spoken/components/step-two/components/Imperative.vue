<script setup lang="ts">
import { nanoid } from '@sa/utils'
import { useRequest } from '@sa/hooks'
import dayjs from 'dayjs'
import CodeCard from '@sa/components/common/code/CodeCard.vue'
import { NFormItem } from 'naive-ui'
import AiGenImageModal from './AiGenImageModal.vue'
import { fetchAIGenerateHTMLCode, fetchGetAIFileInfoList } from '@/service/api'

const formModel
  = defineModel<AgentApi.OralCommunicationInstructTaskInput>('directive')

type FormItem = InstanceType<typeof NFormItem>

const animationFormItemRef = ref<FormItem>()

const currentMode = ref<1 | 2>(1)
const aiPrompt = ref('')

const isGenAi = ref(false)

const previewCode = ref({
  show: false,
  html: '',
})

// 本地生成的临时数据
const localFileList = ref<
  Array<
    AgentApi.AIGenerateHTMLCodeOutput & {
      loading: boolean
    }
  >
>([])

// 验证方式选项
const verificationMethods = [
  { label: '图片题库（系统提供道具图，学生圈选结果）', value: 1 },
  { label: '动画效果（由AI生成画面效果，学生实践）', value: 2 },
  { label: '文字描述（学生文字回复执行结果）', value: 3 },
]

const { data: aiFileList, run: fetchAiFileList } = useRequest(
  () => {
    return fetchGetAIFileInfoList()
  },
  {
    manual: true,
  },
)

// 简化的计算属性，只处理URL字符串数组
const ImgUrls = computed({
  get() {
    return (formModel.value!.ImgUrls ?? [])
      .map(item => item.ImgUrl)
      .filter(Boolean) as string[]
  },
  set(urls: string[]) {
    // 将URL数组转换回 AgentApi.ImgInput 格式
    const newImgUrls = urls.map((url) => {
      // 检查是否是AI生成的图片
      const existingItem = formModel.value!.ImgUrls?.find(
        item => item.ImgUrl === url,
      )
      return {
        ImgUrl: url,
        Type: existingItem?.Type || 1, // 默认为上传类型
      }
    })
    formModel.value!.ImgUrls = newImgUrls
  },
})

const aiGenModal = ref({
  show: false,
})

async function aiGenImage() {
  aiGenModal.value.show = true
}

function onInsertImage(img: string) {
  const newImage: AgentApi.ImgInput = {
    ImgUrl: img,
    Type: 2, // AI生成类型
  }
  aiGenModal.value.show = false
  if (!formModel.value!.ImgUrls) {
    formModel.value!.ImgUrls = [newImage]
    return
  }
  formModel.value!.ImgUrls.push(newImage)
}

async function genAiCode() {
  if (!aiPrompt.value) {
    window.$message?.warning('请输入动画要求')
    return
  }
  const prompt = aiPrompt.value
  aiPrompt.value = ''
  const newItem = reactive({
    Id: nanoid(),
    Name: '-',
    CreateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    HtmlCode: '',
    loading: true,
  })
  const index = localFileList.value.length
  localFileList.value.push(newItem)
  isGenAi.value = true
  try {
    const { error, data } = await fetchAIGenerateHTMLCode({
      Prompt: prompt,
    })
    if (error) {
      localFileList.value.splice(index, 1)
      return
    }
    newItem.HtmlCode = data!.HtmlCode!
    newItem.Name = data!.Name!
    newItem.loading = false
  }
  catch {
  }
  finally {
    isGenAi.value = false
  }
}

function validateField(formItem?: FormItem) {
  formItem?.validate()
}

// 预览
function preview(html: string) {
  previewCode.value.show = true
  previewCode.value.html = html
}
</script>

<template>
  <!-- 指令内容 -->
  <NFormItem
    label="指令内容"
    path="InstructContent"
    :rule="[
      {
        required: true,
        message: '请输入指令内容',
        trigger: ['blur', 'input'],
      },
    ]"
  >
    <NInput
      v-model:value="formModel!.InstructContent"
      type="textarea"
      placeholder="请输入指令内容"
      :rows="4"
      clearable
      maxlength="500"
      show-count
    />
  </NFormItem>

  <!-- 验证方式 -->
  <NFormItem
    label="验证方式"
    path="VerificationMode"
    :rule="[
      {
        required: true,
        type: 'number',
        message: '请选择验证方式',
        trigger: ['blur', 'change'],
      },
    ]"
  >
    <NRadioGroup v-model:value="formModel!.VerificationMode">
      <NSpace vertical>
        <NRadio
          v-for="method in verificationMethods"
          :key="method.value"
          :value="method.value"
        >
          {{ method.label }}
        </NRadio>
      </NSpace>
    </NRadioGroup>
  </NFormItem>

  <!-- 图片 -->
  <template v-if="formModel?.VerificationMode === 1">
    <NFormItem
      label="图片上传"
      path="ImgUrls"
      :rule="[
        {
          required: true,
          type: 'array',
          message: '请上传图片',
          trigger: ['blur', 'change'],
        },
      ]"
    >
      <div class="w-full">
        <!-- 操作按钮 -->
        <NButton
          v-if="ImgUrls.length < 5"
          class="mb-24px"
          type="primary"
          @click="aiGenImage"
        >
          AI自动生成
        </NButton>
        <!-- 上传图片 -->
        <FileUpload v-model="ImgUrls" class="mb-24px" :max="5" />
      </div>
    </NFormItem>
  </template>

  <!-- 动画效果 -->
  <template v-if="formModel?.VerificationMode === 2">
    <NFormItem
      ref="animationFormItemRef"
      label=" "
      path="HtmlFileInfo.Url"
      :rule="[
        {
          required: true,
          type: 'string',
          message: '请选择',
          trigger: ['change'],
        },
      ]"
    >
      <div class="w-full">
        <div
          class="flex items-center border-b-1 border-gray-200 border-solid pb-12px"
        >
          <div
            class="flex cursor-pointer items-center"
            :class="[{ 'text-[#44A9FB]': currentMode === 1 }]"
            @click="currentMode = 1"
          >
            <icon-local-ic-ai />
            <span class="ml-8px mr-100px">AI生成</span>
          </div>
          <div
            class="flex cursor-pointer items-center"
            :class="[{ 'text-[#44A9FB]': currentMode === 2 }]"
            @click="
              () => {
                currentMode = 2;
                fetchAiFileList();
              }
            "
          >
            <icon-local-ep-plus />
            <span>从库中添加</span>
          </div>
        </div>

        <NCard class="mt-18px">
          <div class="max-h-400px overflow-hidden">
            <NScrollbar class="max-h-400px">
              <template v-if="currentMode === 1">
                <div v-for="item in localFileList" :key="item.Id" class="mb-16px">
                  <NSpin :show="item.loading">
                    <div class="flex items-center">
                      <CodeCard :item="item" @preview="preview" />
                      <NRadio
                        class="ml-16px"
                        :checked="formModel!.HtmlFileInfo?.Url === item.HtmlCode"
                        @change="
                          () => {
                            formModel!.HtmlFileInfo = {
                              CreateTime: item.CreateTime,
                              Name: item.Name,
                              Url: item.HtmlCode,
                              Id: item.Id,

                              // html代码
                              _isHtmlCode: true,
                            };
                            validateField(animationFormItemRef);
                          }
                        "
                      />
                    </div>
                  </NSpin>
                </div>

                <NEmpty v-if="!localFileList.length" description="暂无数据" />
              </template>
              <template v-if="currentMode === 2">
                <div v-for="item in aiFileList" :key="item.Id" class="mb-16px">
                  <div class="flex items-center">
                    <CodeCard :item="item" @preview="preview" />
                    <NRadio
                      class="ml-16px"
                      :checked="formModel!.HtmlFileInfo?.Url === item.Url"
                      @change="
                        () => {
                          formModel!.HtmlFileInfo = {
                            CreateTime: item.CreateTime,
                            Name: item.Name,
                            Url: item.Url,
                            Id: item.Id,

                            // html代码
                            _isHtmlCode: false,
                          };
                          validateField(animationFormItemRef);
                        }
                      "
                    />
                  </div>
                </div>

                <NEmpty
                  v-if="!(aiFileList ?? []).length"
                  description="暂无数据"
                />
              </template>
            </NScrollbar>
          </div>
          <div v-if="currentMode === 1" class="mt-16px flex items-center">
            <NInput
              v-model:value="aiPrompt"
              type="textarea"
              status="success"
              placeholder="请输入动画要求"
            />
            <NButton

              quaternary
              strong
              class="ml-8px"
              :loading="isGenAi"
              @click="genAiCode"
            >
              发送
            </NButton>
          </div>
        </NCard>
      </div>
    </NFormItem>
  </template>

  <!-- 成果要求 -->
  <NFormItem
    label="成果要求"
    path="Result"
    :rule="[
      {
        required: true,
        message: '请输入成果要求',
        trigger: ['blur', 'input'],
      },
      {
        min: 5,
        max: 500,
        message: '成果要求长度应在5-500字符之间',
        trigger: ['blur', 'input'],
      },
    ]"
  >
    <NInput
      v-model:value="formModel!.Result"
      type="textarea"
      placeholder="请输入成果要求"
      :rows="4"
      clearable
      maxlength="500"
      show-count
    />
  </NFormItem>

  <AiGenImageModal v-model:modal="aiGenModal" @insert="onInsertImage" />
</template>

<style scoped></style>
