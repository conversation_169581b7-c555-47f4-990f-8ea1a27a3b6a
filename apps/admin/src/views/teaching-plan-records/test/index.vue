<script setup>
import { computed, ref } from 'vue'
import { Bold, ClassicEditor, Essentials, Italic, Paragraph, SourceEditing } from 'ckeditor5'
import { Ckeditor } from '@ckeditor/ckeditor5-vue'

import 'ckeditor5/ckeditor5.css'
import { Mathlive, MathlivePanelview } from './components/plugins'

const data = ref('<p>Hello world!</p>')

const config = computed(() => {
  return {
    licenseKey: 'GPL', // Or 'GPL'.
    plugins: [Essentials, Paragraph, Bold, Italic, Mathlive, MathlivePanelview, SourceEditing],
    toolbar: ['undo', 'redo', '|', 'bold', 'italic', '|', 'formatPainter', 'mathlive', 'SourceEditing'],

    mathlive: {
      openPanelWhenEquationSelected: true,
      renderMathPanel: (element) => {
        let panelView = new MathlivePanelview()

        panelView.mount(element)

        return () => {
          panelView?.destroy()
          panelView = null
        }
      },
      output: {
        type: 'span',
        attributes: {
          class: 'math-tex',
        },
      },
    },
  }
})
</script>

<template>
  <Ckeditor
    v-model="data"
    :editor="ClassicEditor"
    :config="config"
  />
  <div>{{ data }}</div>
</template>
