export default {
  'x ={-b \\pm \\sqrt{b^2-4ac}\\over 2a} ': '<math xmlns="http://www.w3.org/1998/Math/MathML" display="block"><mi>x</mi><mo>=</mo><mrow><mfrac><mrow><mo>−</mo><mi>b</mi><mo>±</mo><msqrt><msup><mi>b</mi><mn>2</mn></msup><mo>−</mo><mn>4</mn><mi>a</mi><mi>c</mi></msqrt></mrow><mrow><mn>2</mn><mi>a</mi></mrow></mfrac></mrow></math>',
  '\\left(3-4\\right)\\left(1+2\\right)': '<math xmlns="http://www.w3.org/1998/Math/MathML" display="block"><mrow data-mjx-texclass="INNER"><mo data-mjx-texclass="OPEN">(</mo><mn>3</mn><mo>−</mo><mn>4</mn><mo data-mjx-texclass="CLOSE">)</mo></mrow><mrow data-mjx-texclass="INNER"><mo data-mjx-texclass="OPEN">(</mo><mn>1</mn><mo>+</mo><mn>2</mn><mo data-mjx-texclass="CLOSE">)</mo></mrow></math>',
  'e^x': '<span class="ML__latex"><span class="ML__strut" style="height:0.72em"></span><span class="ML__base"><span class="ML__mathit">e</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.72em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.31em;display:inline-block;font-size: 70%"><span class="ML__mathit">x</span></span></span></span></span></span></span></span></span>',
  '\\frac{x}{y}': '<span class="ML__latex"><span class="ML__strut" style="height:0.94em"></span><span class="ML__strut--bottom" style="height:1.82em;vertical-align:-0.88em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.94em"><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block"><span class="ML__mathit" style="margin-right:0.04em">y</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.44em;display:inline-block"><span class="ML__mathit">x</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.89em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\sqrt[n]{x}': '<span class="ML__latex"><span class="ML__strut" style="height:0.87em"></span><span class="ML__strut--bottom" style="height:1.21em;vertical-align:-0.33em"></span><span class="ML__base"><span class="ML__sqrt-index"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.54em"><span style="top:-3.32em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.22em;display:inline-block;font-size: 50%"><span class="ML__mathit">n</span></span></span></span></span></span></span><span class="ML__sqrt-sign" style="top:-0.01em"><span class="ML__delim-size1">√</span></span><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.44em;display:inline-block"><span class="ML__mathit">x</span></span></span><span style="top:-3.78em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__sqrt-line" style="height:0.04em;display:inline-block"></span></span></span></span></span></span></span>',
  '\\int_{-x}^x\\nolimits': '<span class="ML__latex"><span class="ML__strut" style="height:1.4em"></span><span class="ML__strut--bottom" style="height:2.35em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op" style="margin-right:0.45em">∫</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.4em"><span style="top:-2.1em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.47em;display:inline-block;font-size: 70%"><span class="ML__cmr">−</span><span class="ML__mathit">x</span></span></span><span style="top:-4.08em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.31em;display:inline-block;font-size: 70%"><span class="ML__mathit">x</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span></span></span></span>',
  '\\sum_{i=0}^n\\displaylimits': '<span class="ML__latex"><span class="ML__strut" style="height:1.66em"></span><span class="ML__strut--bottom" style="height:2.93em;vertical-align:-1.27em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.66em"><span class="ML__center" style="top:-1.87em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.47em;display:inline-block;font-size: 70%"><span class="ML__mathit">i</span><span class="ML__cmr">=0</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">∑</span></span></span><span class="ML__center" style="top:-4.3em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.31em;display:inline-block;font-size: 70%"><span class="ML__mathit">n</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.28em"></span></span></span></span></span></span>',
  '\\sin{\\theta}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">sin</span></span><span class="lcGreek ML__mathit" style="margin-right:0.03em">θ</span></span></span>',
  ' \\lim_{n\\rightarrow\\infty}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:1.4em;vertical-align:-0.7em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.7em"><span class="ML__center" style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.31em;display:inline-block;font-size: 70%"><span class="ML__mathit">n</span><span class="ML__cmr">→∞</span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.7em;display:inline-block"><span class="ML__cmr">lim</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.7em"></span></span></span></span></span></span>',
  '\\(\\begin{bmatrix}1 & 0\\\\0 & 1\\end{bmatrix}\\)': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span style="display:inline-block"><span class="ML__delim-size3">[</span><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">1</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">0</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">0</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">1</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span></span><span class="ML__delim-size3">]</span></span></span></span>',
  '\\lbrace\\lparen\\rparen\\rbrace': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__cmr">{()}</span></span></span>',
  '\\ddot{a}': '<span class="ML__latex"><span class="ML__strut" style="height:0.67em"></span><span class="ML__base"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.67em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.44em;display:inline-block"><span class="ML__mathit">a</span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__accent-body" style="height:0.67em;display:inline-block">¨</span></span></span></span></span></span></span>',
  '≜': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__cmr">≜</span></span></span>',
  '\\Omega': '<span class="ML__latex"><span class="ML__strut" style="height:0.69em"></span><span class="ML__base"><span class="ML__cmr">Ω</span></span></span>',
  '\\ce{H2O}': '<span class="ML__latex"><span class="ML__strut" style="height:0.69em"></span><span class="ML__strut--bottom" style="height:0.84em;vertical-align:-0.15em"></span><span class="ML__base"><span class="ML__cmr">H</span><span class="ML__rlap"><span style="opacity:0"><span class="ML__mathit" style="margin-right:0.08em;opacity:0">X</span></span><span class="ML__fix"></span></span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:-0.15em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0;display:inline-block;font-size: 70%"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0;display:inline-block"><span class="ML__cmr">2</span></span></span></span></span></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.15em"></span></span></span></span><span class="ML__cmr">O</span></span></span>',
  '#0^#0': '<span class="ML__latex"><span class="ML__strut" style="height:0.91em"></span><span class="ML__strut--bottom" style="height:1.11em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.91em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span></span></span></span></span></span>',
  '#0_#0': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.99em;vertical-align:-0.28em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.34em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.29em"></span></span></span></span></span></span>',
  '#0_#0^#0': '<span class="ML__latex"><span class="ML__strut" style="height:0.98em"></span><span class="ML__strut--bottom" style="height:1.42em;vertical-align:-0.44em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.98em"><span style="top:-2.69em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.48em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.45em"></span></span></span></span></span></span>',
  '{_#0^#0}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.98em"></span><span class="ML__strut--bottom" style="height:1.42em;vertical-align:-0.44em"></span><span class="ML__base"><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.98em"><span style="top:-2.69em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.48em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.45em"></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  'x_{y^2}': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__strut--bottom" style="height:0.75em;vertical-align:-0.31em"></span><span class="ML__base"><span class="ML__mathit">x</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.35em"><span style="top:-2.82em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.66em;display:inline-block;font-size: 70%"><span class="ML__mathit" style="margin-right:0.04em">y</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.75em"><span style="top:-3.28em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.47em;display:inline-block;font-size: 71.43%"><span class="ML__cmr">2</span></span></span></span></span></span></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.32em"></span></span></span></span></span></span>',
  'e^{-i\\omega t}': '<span class="ML__latex"><span class="ML__strut" style="height:0.88em"></span><span class="ML__base"><span class="ML__mathit">e</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.88em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.52em;display:inline-block;font-size: 70%"><span class="ML__cmr">−</span><span class="ML__mathit">i</span><span class="lcGreek ML__mathit" style="margin-right:0.04em">ω</span><span class="ML__mathit">t</span></span></span></span></span></span></span></span></span>',
  'x^2': '<span class="ML__latex"><span class="ML__strut" style="height:0.87em"></span><span class="ML__base"><span class="ML__mathit">x</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span></span></span></span></span></span>',
  '{_1^n}Y': '<span class="ML__latex"><span class="ML__strut" style="height:0.72em"></span><span class="ML__strut--bottom" style="height:0.97em;vertical-align:-0.24em"></span><span class="ML__base"><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.72em"><span style="top:-2.75em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">1</span></span></span><span style="top:-3.41em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.31em;display:inline-block;font-size: 70%"><span class="ML__mathit">n</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.25em"></span></span></span></span><span class="ML__mathit" style="margin-right:0.23em">Y</span></span></span>',
  'a^2+b^2=c^2': '<span class="ML__latex"><span class="ML__strut" style="height:0.87em"></span><span class="ML__strut--bottom" style="height:0.95em;vertical-align:-0.08em"></span><span class="ML__base"><span class="ML__mathit">a</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span></span></span></span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__mathit">b</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span></span></span></span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">=</span><span style="display:inline-block;width:0.28em"></span><span class="ML__mathit">c</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span></span></span></span></span></span>',
  '\\frac{#0}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1.29em"></span><span class="ML__strut--bottom" style="height:2.18em;vertical-align:-0.88em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.29em"><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.59em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.89em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\tfrac{#0}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.94em"></span><span class="ML__strut--bottom" style="height:1.43em;vertical-align:-0.48em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.94em"><span class="ML__center" style="top:-2.65em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.45em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.49em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '#0/#0': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__cmr">▢/▢</span></span></span>',
  '\\frac{\\pi}{2}': '<span class="ML__latex"><span class="ML__strut" style="height:0.94em"></span><span class="ML__strut--bottom" style="height:1.62em;vertical-align:-0.68em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.94em"><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.65em;display:inline-block"><span class="ML__cmr">2</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.44em;display:inline-block"><span class="lcGreek ML__mathit" style="margin-right:0.04em">π</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.69em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\frac{dy}{dx}': '<span class="ML__latex"><span class="ML__strut" style="height:1.28em"></span><span class="ML__strut--bottom" style="height:1.97em;vertical-align:-0.68em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.28em"><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.7em;display:inline-block"><span class="ML__mathit">dx</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.58em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.89em;display:inline-block"><span class="ML__mathit">d</span><span class="ML__mathit" style="margin-right:0.04em">y</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.69em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\frac{\\Delta y}{\\Delta x}': '<span class="ML__latex"><span class="ML__strut" style="height:1.27em"></span><span class="ML__strut--bottom" style="height:1.96em;vertical-align:-0.68em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.27em"><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.69em;display:inline-block"><span class="ML__cmr">Δ</span><span class="ML__mathit">x</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.58em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.88em;display:inline-block"><span class="ML__cmr">Δ</span><span class="ML__mathit" style="margin-right:0.04em">y</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.69em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\frac{\\partial y}{\\partial x}': '<span class="ML__latex"><span class="ML__strut" style="height:1.28em"></span><span class="ML__strut--bottom" style="height:1.97em;vertical-align:-0.68em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.28em"><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.7em;display:inline-block"><span class="ML__cmr" style="margin-right:0.06em">∂</span><span class="ML__mathit">x</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.58em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.89em;display:inline-block"><span class="ML__cmr" style="margin-right:0.06em">∂</span><span class="ML__mathit" style="margin-right:0.04em">y</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.69em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\frac{\\delta y}{\\delta x}': '<span class="ML__latex"><span class="ML__strut" style="height:1.28em"></span><span class="ML__strut--bottom" style="height:1.97em;vertical-align:-0.68em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.28em"><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.7em;display:inline-block"><span class="lcGreek ML__mathit" style="margin-right:0.04em">δ</span><span class="ML__mathit">x</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.58em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.89em;display:inline-block"><span class="lcGreek ML__mathit" style="margin-right:0.04em">δ</span><span class="ML__mathit" style="margin-right:0.04em">y</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.69em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\frac{1}{x^2 + 1}': '<span class="ML__latex"><span class="ML__strut" style="height:1.15em"></span><span class="ML__strut--bottom" style="height:1.92em;vertical-align:-0.76em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.15em"><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.83em;display:inline-block"><span class="ML__mathit">x</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.75em"><span style="top:-3.28em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span></span></span></span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">1</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.65em;display:inline-block"><span class="ML__cmr">1</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.77em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\sqrt{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.91em"></span><span class="ML__strut--bottom" style="height:1.21em;vertical-align:-0.29em"></span><span class="ML__base"><span style="display:inline-block;height:1.21em"><span class="ML__sqrt-sign" style="top:-0.05em"><span class="ML__delim-size1">√</span></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.91em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3.82em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__sqrt-line" style="height:0.04em;display:inline-block"></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span></span>',
  '\\sqrt[#0]{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.91em"></span><span class="ML__strut--bottom" style="height:1.21em;vertical-align:-0.29em"></span><span class="ML__base"><span class="ML__sqrt-index"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.72em"><span style="top:-3.36em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.45em;display:inline-block;font-size: 50%"><span class="ML__cmr">▢</span></span></span></span></span></span></span><span class="ML__sqrt-sign" style="top:-0.05em"><span class="ML__delim-size1">√</span></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.91em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3.82em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__sqrt-line" style="height:0.04em;display:inline-block"></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\sqrt[2]{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.91em"></span><span class="ML__strut--bottom" style="height:1.21em;vertical-align:-0.29em"></span><span class="ML__base"><span class="ML__sqrt-index"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.69em"><span style="top:-3.36em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.33em;display:inline-block;font-size: 50%"><span class="ML__cmr">2</span></span></span></span></span></span></span><span class="ML__sqrt-sign" style="top:-0.05em"><span class="ML__delim-size1">√</span></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.91em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3.82em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__sqrt-line" style="height:0.04em;display:inline-block"></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\sqrt[3]{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.91em"></span><span class="ML__strut--bottom" style="height:1.21em;vertical-align:-0.29em"></span><span class="ML__base"><span class="ML__sqrt-index"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.69em"><span style="top:-3.36em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.33em;display:inline-block;font-size: 50%"><span class="ML__cmr">3</span></span></span></span></span></span></span><span class="ML__sqrt-sign" style="top:-0.05em"><span class="ML__delim-size1">√</span></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.91em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3.82em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__sqrt-line" style="height:0.04em;display:inline-block"></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\sqrt{a^2+b^2}': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.21em;vertical-align:-0.15em"></span><span class="ML__base"><span style="display:inline-block;height:1.21em"><span class="ML__sqrt-sign" style="top:-0.19em"><span class="ML__delim-size1">√</span></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.05em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.95em;display:inline-block"><span class="ML__mathit">a</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span></span></span></span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__mathit">b</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span></span></span></span></span></span><span style="top:-3.96em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__sqrt-line" style="height:0.04em;display:inline-block"></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.09em"></span></span></span></span></span></span>',
  '\\frac{-b\\pm\\sqrt{b^2-4ac}}{2a}': '<span class="ML__latex"><span class="ML__strut" style="height:1.39em"></span><span class="ML__strut--bottom" style="height:2.08em;vertical-align:-0.68em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.39em"><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.65em;display:inline-block"><span class="ML__cmr">2</span><span class="ML__mathit">a</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.55em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1em;display:inline-block"><span class="ML__cmr">−</span><span class="ML__mathit">b</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">±</span><span style="display:inline-block;width:0.23em"></span><span style="display:inline-block;height:1em"><span class="ML__sqrt-sign" style="top:-0.03em"><span class="ML__small-delim">√</span></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.84em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.83em;display:inline-block"><span class="ML__mathit">b</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.75em"><span style="top:-3.28em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span></span></span></span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">−</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">4</span><span class="ML__mathit">ac</span></span></span><span style="top:-3.75em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__sqrt-line" style="height:0.04em;display:inline-block"></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.09em"></span></span></span></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.69em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\int #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.36em"></span><span class="ML__strut--bottom" style="height:2.23em;vertical-align:-0.86em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op" style="margin-right:0.45em">∫</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\int_{#0}^{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.58em"></span><span class="ML__strut--bottom" style="height:2.62em;vertical-align:-1.03em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op" style="margin-right:0.45em">∫</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.58em"><span style="top:-2.1em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-4.08em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.04em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\int_{#0}^{#0}\\displaylimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:2.21em"></span><span class="ML__strut--bottom" style="height:3.96em;vertical-align:-1.75em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.21em"><span class="ML__center" style="top:-1.84em;margin-left:-0.44em"><span class="ML__pstrut" style="height:3.37em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.36em"><span class="ML__pstrut" style="height:3.37em"></span><span style="height:2.23em;display:inline-block"><span class="ML__op-symbol ML__large-op" style="margin-right:0.45em">∫</span></span></span><span class="ML__center" style="top:-4.97em;margin-left:0.45em"><span class="ML__pstrut" style="height:3.37em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.76em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\iint #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.36em"></span><span class="ML__strut--bottom" style="height:2.23em;vertical-align:-0.86em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op" style="margin-right:0.45em">∬</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\iint_{#0}^{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.58em"></span><span class="ML__strut--bottom" style="height:2.62em;vertical-align:-1.03em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op" style="margin-right:0.45em">∬</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.58em"><span style="top:-2.1em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-4.08em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.04em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\iint_{#0}^{#0}\\displaylimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:2.21em"></span><span class="ML__strut--bottom" style="height:3.96em;vertical-align:-1.75em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.21em"><span class="ML__center" style="top:-1.84em;margin-left:-0.44em"><span class="ML__pstrut" style="height:3.37em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.36em"><span class="ML__pstrut" style="height:3.37em"></span><span style="height:2.23em;display:inline-block"><span class="ML__op-symbol ML__large-op" style="margin-right:0.45em">∬</span></span></span><span class="ML__center" style="top:-4.97em;margin-left:0.45em"><span class="ML__pstrut" style="height:3.37em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.76em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\iiint #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.36em"></span><span class="ML__strut--bottom" style="height:2.23em;vertical-align:-0.86em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op" style="margin-right:0.45em">∭</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\iiint_{#0}^{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.58em"></span><span class="ML__strut--bottom" style="height:2.62em;vertical-align:-1.03em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op" style="margin-right:0.45em">∭</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.58em"><span style="top:-2.1em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-4.08em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.04em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\iiint_{#0}^{#0}\\displaylimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:2.21em"></span><span class="ML__strut--bottom" style="height:3.96em;vertical-align:-1.75em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.21em"><span class="ML__center" style="top:-1.84em;margin-left:-0.44em"><span class="ML__pstrut" style="height:3.37em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.36em"><span class="ML__pstrut" style="height:3.37em"></span><span style="height:2.23em;display:inline-block"><span class="ML__op-symbol ML__large-op" style="margin-right:0.45em">∭</span></span></span><span class="ML__center" style="top:-4.97em;margin-left:0.45em"><span class="ML__pstrut" style="height:3.37em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.76em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\oint #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.36em"></span><span class="ML__strut--bottom" style="height:2.23em;vertical-align:-0.86em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op" style="margin-right:0.45em">∮</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\oint_{#0}^{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.58em"></span><span class="ML__strut--bottom" style="height:2.62em;vertical-align:-1.03em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op" style="margin-right:0.45em">∮</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.58em"><span style="top:-2.1em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-4.08em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.04em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\oint_{#0}^{#0}\\displaylimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:2.21em"></span><span class="ML__strut--bottom" style="height:3.96em;vertical-align:-1.75em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.21em"><span class="ML__center" style="top:-1.84em;margin-left:-0.44em"><span class="ML__pstrut" style="height:3.37em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.36em"><span class="ML__pstrut" style="height:3.37em"></span><span style="height:2.23em;display:inline-block"><span class="ML__op-symbol ML__large-op" style="margin-right:0.45em">∮</span></span></span><span class="ML__center" style="top:-4.97em;margin-left:0.45em"><span class="ML__pstrut" style="height:3.37em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.76em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\oiint #0': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">∯</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\oiint_{#0}^{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:0.98em"></span><span class="ML__strut--bottom" style="height:1.42em;vertical-align:-0.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">∯</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.98em"><span style="top:-2.69em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.48em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.45em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\oiint_{#0}^{#0}\\displaylimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.55em"></span><span class="ML__strut--bottom" style="height:2.64em;vertical-align:-1.09em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"><span class="ML__center" style="top:-2.14em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__op-symbol ML__large-op">∯</span></span></span><span class="ML__center" style="top:-3.95em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.1em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\oiiint #0': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">∰</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\oiiint_{#0}^{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:0.98em"></span><span class="ML__strut--bottom" style="height:1.42em;vertical-align:-0.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">∰</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.98em"><span style="top:-2.69em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.48em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.45em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\oiiint_{#0}^{#0}\\displaylimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.55em"></span><span class="ML__strut--bottom" style="height:2.64em;vertical-align:-1.09em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"><span class="ML__center" style="top:-2.14em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__op-symbol ML__large-op">∰</span></span></span><span class="ML__center" style="top:-3.95em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.1em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\sum #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.61em;vertical-align:-0.55em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">∑</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\sum_{#0}^{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.9em"></span><span class="ML__strut--bottom" style="height:3.34em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.9em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">∑</span></span></span><span class="ML__center" style="top:-4.35em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\sum_{#0}^{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.27em"></span><span class="ML__strut--bottom" style="height:2em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">∑</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.27em"><span style="top:-2.41em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.77em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\sum_{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.06em"></span><span class="ML__strut--bottom" style="height:2.5em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.06em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">∑</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\sum_{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.78em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">∑</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:-0.09em"><span style="top:-2.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\prod #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.61em;vertical-align:-0.55em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">∏</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\prod_{#0}^{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.9em"></span><span class="ML__strut--bottom" style="height:3.34em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.9em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">∏</span></span></span><span class="ML__center" style="top:-4.35em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\prod_{#0}^{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.27em"></span><span class="ML__strut--bottom" style="height:2em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">∏</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.27em"><span style="top:-2.41em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.77em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\prod_{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.06em"></span><span class="ML__strut--bottom" style="height:2.5em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.06em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">∏</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\prod_{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.78em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">∏</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:-0.09em"><span style="top:-2.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\coprod #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.61em;vertical-align:-0.55em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">∐</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\coprod_{#0}^{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.9em"></span><span class="ML__strut--bottom" style="height:3.34em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.9em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">∐</span></span></span><span class="ML__center" style="top:-4.35em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\coprod_{#0}^{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.27em"></span><span class="ML__strut--bottom" style="height:2em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">∐</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.27em"><span style="top:-2.41em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.77em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\coprod_{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.06em"></span><span class="ML__strut--bottom" style="height:2.5em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.06em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">∐</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\coprod_{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.78em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">∐</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:-0.09em"><span style="top:-2.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigcup #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.61em;vertical-align:-0.55em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">⋃</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigcup_{#0}^{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.9em"></span><span class="ML__strut--bottom" style="height:3.34em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.9em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">⋃</span></span></span><span class="ML__center" style="top:-4.35em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigcup_{#0}^{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.27em"></span><span class="ML__strut--bottom" style="height:2em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">⋃</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.27em"><span style="top:-2.41em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.77em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigcup_{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.06em"></span><span class="ML__strut--bottom" style="height:2.5em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.06em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">⋃</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigcup_{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.78em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">⋃</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:-0.09em"><span style="top:-2.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigcap #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.61em;vertical-align:-0.55em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">⋂</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigcap_{#0}^{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.9em"></span><span class="ML__strut--bottom" style="height:3.34em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.9em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">⋂</span></span></span><span class="ML__center" style="top:-4.35em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigcap_{#0}^{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.27em"></span><span class="ML__strut--bottom" style="height:2em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">⋂</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.27em"><span style="top:-2.41em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.77em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigcap_{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.06em"></span><span class="ML__strut--bottom" style="height:2.5em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.06em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">⋂</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigcap_{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.78em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">⋂</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:-0.09em"><span style="top:-2.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigvee #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.61em;vertical-align:-0.55em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">⋁</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigvee_{#0}^{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.9em"></span><span class="ML__strut--bottom" style="height:3.34em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.9em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">⋁</span></span></span><span class="ML__center" style="top:-4.35em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigvee_{#0}^{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.27em"></span><span class="ML__strut--bottom" style="height:2em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">⋁</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.27em"><span style="top:-2.41em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.77em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigvee_{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.06em"></span><span class="ML__strut--bottom" style="height:2.5em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.06em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">⋁</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigvee_{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.78em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">⋁</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:-0.09em"><span style="top:-2.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigwedge #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.61em;vertical-align:-0.55em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">⋀</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigwedge_{#0}^{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.9em"></span><span class="ML__strut--bottom" style="height:3.34em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.9em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">⋀</span></span></span><span class="ML__center" style="top:-4.35em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigwedge_{#0}^{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.27em"></span><span class="ML__strut--bottom" style="height:2em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">⋀</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.27em"><span style="top:-2.41em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.77em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigwedge_{#0} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.06em"></span><span class="ML__strut--bottom" style="height:2.5em;vertical-align:-1.44em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.06em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">⋀</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\bigwedge_{#0}\\nolimits #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.05em"></span><span class="ML__strut--bottom" style="height:1.78em;vertical-align:-0.72em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__op-symbol ML__large-op">⋀</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:-0.09em"><span style="top:-2.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.73em"></span></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\sum_{k}\\binom{n}{k}': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.76em;vertical-align:-1.3em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.06em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.49em;display:inline-block;font-size: 70%"><span class="ML__mathit" style="margin-right:0.04em">k</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">∑</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.31em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__mfrac"><span class="ML__delim-size3">(</span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.94em"><span class="ML__center" style="top:-3.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.44em;display:inline-block"><span class="ML__mathit">n</span></span></span><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.7em;display:inline-block"><span class="ML__mathit" style="margin-right:0.04em">k</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.69em"></span></span></span><span class="ML__delim-size3">)</span></span></span></span>',
  '\\sum_{i=0}^{n} #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.66em"></span><span class="ML__strut--bottom" style="height:2.93em;vertical-align:-1.27em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.66em"><span class="ML__center" style="top:-1.87em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.47em;display:inline-block;font-size: 70%"><span class="ML__mathit">i</span><span class="ML__cmr">=0</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">∑</span></span></span><span class="ML__center" style="top:-4.3em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.31em;display:inline-block;font-size: 70%"><span class="ML__mathit">n</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.28em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\sum_{0\\le i \\le m \\atop 0 < j < n}P\\left(i,j\\right)': '<span class="ML__latex"><span class="ML__strut" style="height:1.06em"></span><span class="ML__strut--bottom" style="height:3.02em;vertical-align:-1.96em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.06em"><span class="ML__center" style="top:-1.65em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.16em;display:inline-block;font-size: 70%"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.98em"><span class="ML__center" style="top:-3.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.57em;display:inline-block;font-size: 71.43%"><span class="ML__cmr">0≤</span><span class="ML__mathit">i</span><span class="ML__cmr">≤</span><span class="ML__mathit">m</span></span></span><span class="ML__center" style="top:-2.46em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.61em;display:inline-block;font-size: 71.43%"><span class="ML__cmr">0<</span><span class="ML__mathit" style="margin-right:0.06em">j</span><span class="ML__cmr"><</span><span class="ML__mathit">n</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.68em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">∑</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.97em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__mathit" style="margin-right:0.14em">P</span><span style="display:inline-block;width:0.17em"></span><span class="ML__left-right" style="margin-top:-0.19444em;height:0.85396em"><span class="ML__small-delim ML__open">(</span><span class="ML__mathit">i</span><span class="ML__cmr">,</span><span style="display:inline-block;width:0.17em"></span><span class="ML__mathit" style="margin-right:0.06em">j</span><span class="ML__small-delim ML__close">)</span></span></span></span>',
  '\\prod_{k=1}^{n}A_k': '<span class="ML__latex"><span class="ML__strut" style="height:1.66em"></span><span class="ML__strut--bottom" style="height:2.96em;vertical-align:-1.3em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.66em"><span class="ML__center" style="top:-1.84em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.49em;display:inline-block;font-size: 70%"><span class="ML__mathit" style="margin-right:0.04em">k</span><span class="ML__cmr">=1</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">∏</span></span></span><span class="ML__center" style="top:-4.3em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.31em;display:inline-block;font-size: 70%"><span class="ML__mathit">n</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.31em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__mathit">A</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.34em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.49em;display:inline-block;font-size: 70%"><span class="ML__mathit" style="margin-right:0.04em">k</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.15em"></span></span></span></span></span></span>',
  '\\bigcup_{n=1}^{m}\\left(X_n\\cap Y_n\\right)': '<span class="ML__latex"><span class="ML__strut" style="height:1.66em"></span><span class="ML__strut--bottom" style="height:2.92em;vertical-align:-1.26em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.66em"><span class="ML__center" style="top:-1.88em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__mathit">n</span><span class="ML__cmr">=1</span></span></span><span class="ML__center" style="top:-3.05em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:1.61em;display:inline-block"><span class="ML__op-symbol ML__large-op">⋃</span></span></span><span class="ML__center" style="top:-4.3em"><span class="ML__pstrut" style="height:3.05em"></span><span style="height:0.31em;display:inline-block;font-size: 70%"><span class="ML__mathit">m</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.27em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__left-right" style="margin-top:-0.15em;height:0.83333em"><span class="ML__small-delim ML__open">(</span><span class="ML__mathit" style="margin-right:0.08em">X</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.16em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.31em;display:inline-block;font-size: 70%"><span class="ML__mathit">n</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.15em"></span></span></span></span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">∩</span><span style="display:inline-block;width:0.23em"></span><span class="ML__mathit" style="margin-right:0.23em">Y</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.16em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.31em;display:inline-block;font-size: 70%"><span class="ML__mathit">n</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.15em"></span></span></span></span><span class="ML__small-delim ML__close">)</span></span></span></span>',
  '\\sin{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">sin</span></span><span class="ML__cmr">▢</span></span></span>',
  '\\cos{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">cos</span></span><span class="ML__cmr">▢</span></span></span>',
  '\\tan{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">tan</span></span><span class="ML__cmr">▢</span></span></span>',
  '\\csc{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">csc</span></span><span class="ML__cmr">▢</span></span></span>',
  '\\sec{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">sec</span></span><span class="ML__cmr">▢</span></span></span>',
  '\\cot{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">cot</span></span><span class="ML__cmr">▢</span></span></span>',
  '\\sin^{-1}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.87em"></span><span class="ML__strut--bottom" style="height:1.07em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">sin</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.51em;display:inline-block;font-size: 70%"><span class="ML__cmr">−1</span></span></span></span></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\cos^{-1}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.87em"></span><span class="ML__strut--bottom" style="height:1.07em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">cos</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.51em;display:inline-block;font-size: 70%"><span class="ML__cmr">−1</span></span></span></span></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\tan^{-1}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.87em"></span><span class="ML__strut--bottom" style="height:1.07em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">tan</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.51em;display:inline-block;font-size: 70%"><span class="ML__cmr">−1</span></span></span></span></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\csc^{-1}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.87em"></span><span class="ML__strut--bottom" style="height:1.07em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">csc</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.51em;display:inline-block;font-size: 70%"><span class="ML__cmr">−1</span></span></span></span></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\sec^{-1}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.87em"></span><span class="ML__strut--bottom" style="height:1.07em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">sec</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.51em;display:inline-block;font-size: 70%"><span class="ML__cmr">−1</span></span></span></span></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\cot^{-1}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.87em"></span><span class="ML__strut--bottom" style="height:1.07em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">cot</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.51em;display:inline-block;font-size: 70%"><span class="ML__cmr">−1</span></span></span></span></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\sinh{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">sinh</span></span><span class="ML__cmr">▢</span></span></span>',
  '\\cosh{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">cosh</span></span><span class="ML__cmr">▢</span></span></span>',
  '\\tanh{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">tanh</span></span><span class="ML__cmr">▢</span></span></span>',
  '\\coth{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">coth</span></span><span class="ML__cmr">▢</span></span></span>',
  '\\sinh^{-1}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.88em"></span><span class="ML__strut--bottom" style="height:1.08em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">sinh</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.88em"><span style="top:-3.42em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.51em;display:inline-block;font-size: 70%"><span class="ML__cmr">−1</span></span></span></span></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\cosh^{-1}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.88em"></span><span class="ML__strut--bottom" style="height:1.08em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">cosh</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.88em"><span style="top:-3.42em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.51em;display:inline-block;font-size: 70%"><span class="ML__cmr">−1</span></span></span></span></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\tanh^{-1}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.88em"></span><span class="ML__strut--bottom" style="height:1.08em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">tanh</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.88em"><span style="top:-3.42em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.51em;display:inline-block;font-size: 70%"><span class="ML__cmr">−1</span></span></span></span></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\coth^{-1}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.88em"></span><span class="ML__strut--bottom" style="height:1.08em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">coth</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.88em"><span style="top:-3.42em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.51em;display:inline-block;font-size: 70%"><span class="ML__cmr">−1</span></span></span></span></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\cos{2x}': '<span class="ML__latex"><span class="ML__strut" style="height:0.65em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">cos</span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">2</span><span class="ML__mathit">x</span></span></span>',
  '\\tan{\\theta}=\\frac{\\sin{\\theta}}{\\cos{\\theta}}': '<span class="ML__latex"><span class="ML__strut" style="height:1.2em"></span><span class="ML__strut--bottom" style="height:1.89em;vertical-align:-0.68em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">tan</span></span><span class="lcGreek ML__mathit" style="margin-right:0.03em">θ</span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">=</span><span style="display:inline-block;width:0.28em"></span><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.2em"><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.7em;display:inline-block"><span class="ML__op-group"><span class="ML__cmr">cos</span></span><span class="lcGreek ML__mathit" style="margin-right:0.03em">θ</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.7em;display:inline-block"><span class="ML__op-group"><span class="ML__cmr">sin</span></span><span class="lcGreek ML__mathit" style="margin-right:0.03em">θ</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.69em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\log_#0{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:1.07em;vertical-align:-0.36em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">log</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.27em"><span style="top:-2.77em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.37em"></span></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\log{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">log</span></span><span class="ML__cmr">▢</span></span></span>',
  '\\lim_{#0}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:1.6em;vertical-align:-0.89em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.7em"><span class="ML__center" style="top:-2.34em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.7em;display:inline-block"><span class="ML__cmr">lim</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.9em"></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\min_{#0}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:1.6em;vertical-align:-0.89em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.67em"><span class="ML__center" style="top:-2.34em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.67em;display:inline-block"><span class="ML__cmr">min</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.9em"></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\max_{#0}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:1.6em;vertical-align:-0.89em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.44em"><span class="ML__center" style="top:-2.34em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.44em;display:inline-block"><span class="ML__cmr">max</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.9em"></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\ln{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__cmr">ln</span></span><span class="ML__cmr">▢</span></span></span>',
  '\\lim_{n\\rightarrow\\infty}{\\left(1+\\frac{1}{n}\\right)^n}': '<span class="ML__latex"><span class="ML__strut" style="height:1.19em"></span><span class="ML__strut--bottom" style="height:1.89em;vertical-align:-0.7em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.7em"><span class="ML__center" style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.31em;display:inline-block;font-size: 70%"><span class="ML__mathit">n</span><span class="ML__cmr">→∞</span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.7em;display:inline-block"><span class="ML__cmr">lim</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.7em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__left-right" style="margin-top:-0.686em;height:1.8304399999999998em"><span class="ML__open ML__delim-size2">(</span><span class="ML__cmr">1</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.15em"><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.44em;display:inline-block"><span class="ML__mathit">n</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.65em;display:inline-block"><span class="ML__cmr">1</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.69em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span><span class="ML__close ML__delim-size2">)</span></span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.19em"><span style="top:-3.87em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.31em;display:inline-block;font-size: 70%"><span class="ML__mathit">n</span></span></span></span></span></span></span></span></span>',
  '\\max_{0\\le x\\le 1}{xe^{-x^2}}': '<span class="ML__latex"><span class="ML__strut" style="height:1.04em"></span><span class="ML__strut--bottom" style="height:1.85em;vertical-align:-0.81em"></span><span class="ML__base"><span class="ML__op-group"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.44em"><span class="ML__center" style="top:-2.38em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.55em;display:inline-block;font-size: 70%"><span class="ML__cmr">0≤</span><span class="ML__mathit">x</span><span class="ML__cmr">≤1</span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.44em;display:inline-block"><span class="ML__cmr">max</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.82em"></span></span></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__mathit">xe</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.04em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.69em;display:inline-block;font-size: 70%"><span class="ML__cmr">−</span><span class="ML__mathit">x</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.9em"><span style="top:-3.43em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.47em;display:inline-block;font-size: 71.43%"><span class="ML__cmr">2</span></span></span></span></span></span></span></span></span></span></span></span></span></span></span>',
  '\\begin{matrix} #0 & #0 \\\\ \\end{matrix}': '<span class="ML__latex"><span class="ML__strut" style="height:0.85em"></span><span class="ML__strut--bottom" style="height:1.2em;vertical-align:-0.35em"></span><span class="ML__base"><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.85em"><span style="top:-3.01em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.35em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.85em"><span style="top:-3.01em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.35em"></span></span></span></span></span></span></span>',
  '\\begin{matrix} #0 \\\\ #0 \\\\ \\end{matrix}': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span></span></span></span>',
  '\\begin{matrix} #0 & #0 & #0 \\\\ \\end{matrix}': '<span class="ML__latex"><span class="ML__strut" style="height:0.85em"></span><span class="ML__strut--bottom" style="height:1.2em;vertical-align:-0.35em"></span><span class="ML__base"><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.85em"><span style="top:-3.01em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.35em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.85em"><span style="top:-3.01em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.35em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.85em"><span style="top:-3.01em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.35em"></span></span></span></span></span></span></span>',
  '\\begin{matrix} #0 \\\\ #0 \\\\ #0 \\\\ \\end{matrix}': '<span class="ML__latex"><span class="ML__strut" style="height:2.05em"></span><span class="ML__strut--bottom" style="height:3.6em;vertical-align:-1.55em"></span><span class="ML__base"><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.05em"><span style="top:-4.21em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-1.81em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"></span></span></span></span></span></span></span>',
  '\\begin{matrix} #0 & #0 \\\\ #0 & #0 \\\\ \\end{matrix}': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span></span></span></span>',
  '\\begin{matrix} #0 & #0 & #0 \\\\ #0 & #0 & #0 \\\\ \\end{matrix}': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span></span></span></span>',
  '\\begin{matrix} #0 & #0 \\\\ #0 & #0 \\\\ #0 & #0 \\\\ \\end{matrix}': '<span class="ML__latex"><span class="ML__strut" style="height:2.05em"></span><span class="ML__strut--bottom" style="height:3.6em;vertical-align:-1.55em"></span><span class="ML__base"><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.05em"><span style="top:-4.21em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-1.81em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.05em"><span style="top:-4.21em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-1.81em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"></span></span></span></span></span></span></span>',
  '\\begin{matrix} #0 & #0 & #0 \\\\ #0 & #0 & #0 \\\\ #0 & #0 & #0 \\\\ \\end{matrix}': '<span class="ML__latex"><span class="ML__strut" style="height:2.05em"></span><span class="ML__strut--bottom" style="height:3.6em;vertical-align:-1.55em"></span><span class="ML__base"><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.05em"><span style="top:-4.21em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-1.81em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.05em"><span style="top:-4.21em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-1.81em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.05em"><span style="top:-4.21em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-1.81em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"></span></span></span></span></span></span></span>',
  '\\cdots': '<span class="ML__latex"><span class="ML__strut" style="height:0.31em"></span><span class="ML__strut--bottom" style="height:0.12em;vertical-align:0.19em"></span><span class="ML__base"><span class="ML__cmr">⋯</span></span></span>',
  '\\ldots': '<span class="ML__latex"><span class="ML__strut" style="height:0.12em"></span><span class="ML__base"><span class="ML__cmr">…</span></span></span>',
  '\\vdots': '<span class="ML__latex"><span class="ML__strut" style="height:0.9em"></span><span class="ML__strut--bottom" style="height:0.93em;vertical-align:-0.03em"></span><span class="ML__base"><span class="ML__cmr">⋮</span></span></span>',
  '\\ddots': '<span class="ML__latex"><span class="ML__strut" style="height:0.82em"></span><span class="ML__strut--bottom" style="height:0.72em;vertical-align:0.1em"></span><span class="ML__base"><span class="ML__cmr">⋱</span></span></span>',
  '\\begin{matrix} 1 & 0 \\\\ 0 & 1 \\\\ \\end{matrix}': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">1</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">0</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">0</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">1</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span></span></span></span>',
  '\\begin{matrix} 1 & #0 \\\\ #0 & 1 \\\\ \\end{matrix}': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">1</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">1</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span></span></span></span>',
  '\\begin{matrix} 1 & 0 & 0 \\\\ 0 & 1 & 0 \\\\ 0 & 0 & 1 \\\\ \\end{matrix}': '<span class="ML__latex"><span class="ML__strut" style="height:2.05em"></span><span class="ML__strut--bottom" style="height:3.6em;vertical-align:-1.55em"></span><span class="ML__base"><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.05em"><span style="top:-4.21em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">1</span></span></span><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">0</span></span></span><span style="top:-1.81em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">0</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.05em"><span style="top:-4.21em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">0</span></span></span><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">1</span></span></span><span style="top:-1.81em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">0</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.05em"><span style="top:-4.21em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">0</span></span></span><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">0</span></span></span><span style="top:-1.81em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">1</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"></span></span></span></span></span></span></span>',
  '\\begin{matrix} 1 & #0 & #0 \\\\ #0 & 1 & #0 \\\\ #0 & #0 & 1 \\\\ \\end{matrix}': '<span class="ML__latex"><span class="ML__strut" style="height:2.05em"></span><span class="ML__strut--bottom" style="height:3.6em;vertical-align:-1.55em"></span><span class="ML__base"><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.05em"><span style="top:-4.21em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">1</span></span></span><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-1.81em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.05em"><span style="top:-4.21em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">1</span></span></span><span style="top:-1.81em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.05em"><span style="top:-4.21em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-1.81em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">1</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.55em"></span></span></span></span></span></span></span>',
  '\\left(\\begin{matrix} #0 & #0 \\\\ #0 & #0 \\\\ \\end{matrix}\\right)': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.9500000000000004em;height:2.4000000000000004em"><span class="ML__open ML__delim-size3">(</span><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span></span><span class="ML__close ML__delim-size3">)</span></span></span></span>',
  '\\left[\\begin{matrix} #0 & #0 \\\\ #0 & #0 \\\\ \\end{matrix}\\right]': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.9500000000000004em;height:2.4000000000000004em"><span class="ML__open ML__delim-size3">[</span><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span></span><span class="ML__close ML__delim-size3">]</span></span></span></span>',
  '\\left|\\begin{matrix} #0 & #0 \\\\ #0 & #0 \\\\ \\end{matrix}\\right|': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.9500000000000004em;height:2.4000000000000004em"><span class="ML__open ML__delim-mult"><span class="delim-size1 ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.44em"><span style="top:-1.64em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∣</span></span><span style="top:-2.24em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∣</span></span><span style="top:-2.84em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∣</span></span><span style="top:-3.43em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∣</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span></span><span class="ML__close ML__delim-mult"><span class="delim-size1 ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.44em"><span style="top:-1.64em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∣</span></span><span style="top:-2.24em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∣</span></span><span style="top:-2.84em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∣</span></span><span style="top:-3.43em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∣</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span></span></span></span>',
  '\\left\\Vert \\begin{matrix} #0 & #0 \\\\ #0 & #0 \\\\ \\end{matrix} \\right\\Vert': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.9500000000000004em;height:2.4000000000000004em"><span class="ML__open ML__delim-mult"><span class="delim-size1 ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.44em"><span style="top:-1.64em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∥</span></span><span style="top:-2.24em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∥</span></span><span style="top:-2.84em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∥</span></span><span style="top:-3.43em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∥</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span style="top:-3.61em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.4em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span></span><span class="ML__close ML__delim-mult"><span class="delim-size1 ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.44em"><span style="top:-1.64em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∥</span></span><span style="top:-2.24em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∥</span></span><span style="top:-2.84em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∥</span></span><span style="top:-3.43em"><span class="ML__pstrut" style="height:2.61em"></span><span style="height:0.61em;display:inline-block">∥</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.96em"></span></span></span></span></span></span></span>',
  '\\left(\\begin{matrix} #0 & \\cdots & #0 \\\\ \\vdots & \\ddots & \\vdots \\\\ #0 & \\cdots & #0 \\\\ \\end{matrix}\\right)': '<span class="ML__latex"><span class="ML__strut" style="height:2.08em"></span><span class="ML__strut--bottom" style="height:3.66em;vertical-align:-1.57em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-1.5799999999999998em;height:3.66em"><span class="ML__open ML__delim-mult"><span class="delim-size4 ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.04em"><span style="top:-2.25em"><span class="ML__pstrut" style="height:3.16em"></span><span style="height:1.81em;display:inline-block">⎝</span></span><span style="top:-4.03em"><span class="ML__pstrut" style="height:3.16em"></span><span style="height:1.81em;display:inline-block">⎛</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.56em"></span></span></span></span><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.08em"><span style="top:-4.24em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.98em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.26em;display:inline-block"><span class="ML__cmr">⋮</span></span></span><span style="top:-1.78em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.58em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.08em"><span style="top:-4.24em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">⋯</span></span></span><span style="top:-2.98em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.26em;display:inline-block"><span class="ML__cmr">⋱</span></span></span><span style="top:-1.78em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">⋯</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.58em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.08em"><span style="top:-4.24em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.98em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.26em;display:inline-block"><span class="ML__cmr">⋮</span></span></span><span style="top:-1.78em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.58em"></span></span></span></span></span><span class="ML__close ML__delim-mult"><span class="delim-size4 ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.04em"><span style="top:-2.25em"><span class="ML__pstrut" style="height:3.16em"></span><span style="height:1.81em;display:inline-block">⎠</span></span><span style="top:-4.03em"><span class="ML__pstrut" style="height:3.16em"></span><span style="height:1.81em;display:inline-block">⎞</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.56em"></span></span></span></span></span></span></span>',
  '\\left[\\begin{matrix} #0 & \\cdots & #0 \\\\ \\vdots & \\ddots & \\vdots \\\\ #0 & \\cdots & #0 \\\\ \\end{matrix}\\right]': '<span class="ML__latex"><span class="ML__strut" style="height:2.08em"></span><span class="ML__strut--bottom" style="height:3.66em;vertical-align:-1.57em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-1.5799999999999998em;height:3.66em"><span class="ML__open ML__delim-mult"><span class="delim-size4 ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.04em"><span style="top:-2.25em"><span class="ML__pstrut" style="height:3.16em"></span><span style="height:1.81em;display:inline-block">⎣</span></span><span style="top:-4.03em"><span class="ML__pstrut" style="height:3.16em"></span><span style="height:1.81em;display:inline-block">⎡</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.56em"></span></span></span></span><span class="ML__mtable"><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.08em"><span style="top:-4.24em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.98em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.26em;display:inline-block"><span class="ML__cmr">⋮</span></span></span><span style="top:-1.78em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.58em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.08em"><span style="top:-4.24em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">⋯</span></span></span><span style="top:-2.98em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.26em;display:inline-block"><span class="ML__cmr">⋱</span></span></span><span style="top:-1.78em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">⋯</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.58em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span><span class="col-align-c"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.08em"><span style="top:-4.24em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.98em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.26em;display:inline-block"><span class="ML__cmr">⋮</span></span></span><span style="top:-1.78em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.2em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.58em"></span></span></span></span></span><span class="ML__close ML__delim-mult"><span class="delim-size4 ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.04em"><span style="top:-2.25em"><span class="ML__pstrut" style="height:3.16em"></span><span style="height:1.81em;display:inline-block">⎦</span></span><span style="top:-4.03em"><span class="ML__pstrut" style="height:3.16em"></span><span style="height:1.81em;display:inline-block">⎤</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.56em"></span></span></span></span></span></span></span>',
  '\\left( #0\\right)': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">(</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">)</span></span></span></span>',
  '\\left[ #0\\right]': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">[</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">]</span></span></span></span>',
  '\\left\\{ #0\\right\\}': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">{</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">}</span></span></span></span>',
  '\\langle #0\\rangle': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__cmr">⟨▢⟩</span></span></span>',
  '\\left\\lfloor #0\\right\\rfloor': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">⌊</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">⌋</span></span></span></span>',
  '\\left\\lceil #0\\right\\rceil': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">⌈</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">⌉</span></span></span></span>',
  '\\left| #0\\right|': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">∣</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">∣</span></span></span></span>',
  '\\left\\Vert #0\\right\\Vert': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">∥</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">∥</span></span></span></span>',
  '\\left[ #0\\right[': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">[</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">[</span></span></span></span>',
  '\\left] #0\\right]': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">]</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">]</span></span></span></span>',
  '\\left] #0\\right[': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">]</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">[</span></span></span></span>',
  '〚 #0〛': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__cmr">〚▢〛</span></span></span>',
  '\\left( #0\\middle| #0\\right)': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.25em;height:1em"><span class="ML__small-delim ML__open">(</span><span class="ML__cmr">▢</span><span class="ML__small-delim">∣</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">)</span></span></span></span>',
  '\\left\\{ #0\\middle| #0\\right\\}': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.25em;height:1em"><span class="ML__small-delim ML__open">{</span><span class="ML__cmr">▢</span><span class="ML__small-delim">∣</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">}</span></span></span></span>',
  '\\left\\langle #0\\middle| #0\\right\\rangle': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.25em;height:1em"><span class="ML__small-delim ML__open">⟨</span><span class="ML__cmr">▢</span><span class="ML__small-delim">∣</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">⟩</span></span></span></span>',
  '\\left\\langle #0\\middle| #0\\middle| #0\\right\\rangle': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.25em;height:1em"><span class="ML__small-delim ML__open">⟨</span><span class="ML__cmr">▢</span><span class="ML__small-delim">∣</span><span class="ML__cmr">▢</span><span class="ML__small-delim">∣</span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">⟩</span></span></span></span>',
  '\\left( #0\\right.': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">(</span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\left. #0\\right)': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">)</span></span></span></span>',
  '\\left[ #0\\right.': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">[</span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\left. #0\\right]': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">]</span></span></span></span>',
  '\\left\\{ #0\\right.': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">{</span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\left. #0\\right\\}': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">}</span></span></span></span>',
  '\\left\\langle #0\\right.': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">⟨</span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\left. #0\\rangle\\right': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.25em;height:1em"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢⟩</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\left\\lfloor #0\\right.': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">⌊</span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\left. #0\\right\\rfloor': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">⌋</span></span></span></span>',
  '\\left\\lceil #0\\right.': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">⌈</span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\left. #0\\right\\rceil': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">⌉</span></span></span></span>',
  '\\left| #0\\right.': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">∣</span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\left. #0\\right|': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">∣</span></span></span></span>',
  '\\left\\Vert #0\\right.': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__small-delim ML__open">∥</span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\left. #0\\right\\Vert': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:1em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.2em;height:0.8999999999999999em"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__small-delim ML__close">∥</span></span></span></span>',
  '〚 #0': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__cmr">〚▢</span></span></span>',
  ' #0〛': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__cmr">▢〛</span></span></span>',
  '\\left\\{{{#0}\\atop{#0}}\\right.': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.8859999999999999em;height:2.086em"><span class="ML__open ML__delim-size3">{</span><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.2em"><span class="ML__center" style="top:-3.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.89em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\begin{cases}#0\\\\#0\\\\#0\\end{cases}': '<span class="ML__latex"><span class="ML__strut" style="height:2.41em"></span><span class="ML__strut--bottom" style="height:4.32em;vertical-align:-1.9em"></span><span class="ML__base"><span style="display:inline-block"><span class="ML__delim-mult"><span class="delim-size4 ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.32em"><span style="top:-2.19em"><span class="ML__pstrut" style="height:3.15em"></span><span style="height:0.91em;display:inline-block">⎩</span></span><span style="top:-2.19em"><span class="ML__pstrut" style="height:3.15em"></span><span style="height:0.3em;display:inline-block">⎪</span></span><span style="top:-3.13em"><span class="ML__pstrut" style="height:3.15em"></span><span style="height:1.81em;display:inline-block">⎨</span></span><span style="top:-4.27em"><span class="ML__pstrut" style="height:3.15em"></span><span style="height:0.3em;display:inline-block">⎪</span></span><span style="top:-4.56em"><span class="ML__pstrut" style="height:3.15em"></span><span style="height:0.91em;display:inline-block">⎧</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.86em"></span></span></span></span><span class="ML__mtable"><span class="col-align-l"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:2.41em"><span style="top:-4.41em"><span class="ML__pstrut" style="height:3.01em"></span><span style="height:1.44em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-2.97em"><span class="ML__pstrut" style="height:3.01em"></span><span style="height:1.44em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-1.53em"><span class="ML__pstrut" style="height:3.01em"></span><span style="height:1.44em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.91em"></span></span></span></span><span class="ML__arraycolsep" style="width:1em"></span></span><span class="ML__nulldelimiter" style="width:0.12em"></span></span></span></span>',
  '#0 \\atop #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.2em"></span><span class="ML__strut--bottom" style="height:2.09em;vertical-align:-0.88em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.2em"><span class="ML__center" style="top:-3.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.89em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '#0 \\choose #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__delim-size3">(</span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.2em"><span class="ML__center" style="top:-3.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.89em"></span></span></span><span class="ML__delim-size3">)</span></span></span></span>',
  'n \\choose k': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__delim-size3">(</span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.94em"><span class="ML__center" style="top:-3.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.44em;display:inline-block"><span class="ML__mathit">n</span></span></span><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.7em;display:inline-block"><span class="ML__mathit" style="margin-right:0.04em">k</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.69em"></span></span></span><span class="ML__delim-size3">)</span></span></span></span>',
  '\\left\\langle{n\\atop k}\\right\\rangle': '<span class="ML__latex"><span class="ML__strut" style="height:1.15em"></span><span class="ML__strut--bottom" style="height:1.84em;vertical-align:-0.68em"></span><span class="ML__base"><span class="ML__left-right" style="margin-top:-0.6859999999999999em;height:1.61656em"><span class="ML__open ML__delim-size2">⟨</span><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.94em"><span class="ML__center" style="top:-3.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.44em;display:inline-block"><span class="ML__mathit">n</span></span></span><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.7em;display:inline-block"><span class="ML__mathit" style="margin-right:0.04em">k</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.69em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span><span class="ML__close ML__delim-size2">⟩</span></span></span></span>',
  'f\\left(x\\right)=\\left\\{{{-x,x<0}\\atop{\\enspace x,x\\geq0}}\\right.': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:2.41em;vertical-align:-0.95em"></span><span class="ML__base"><span class="ML__mathit" style="margin-right:0.11em">f</span><span style="display:inline-block;width:0.17em"></span><span class="ML__left-right" style="margin-top:0em;height:0.43056em"><span class="ML__small-delim ML__open">(</span><span class="ML__mathit">x</span><span class="ML__small-delim ML__close">)</span></span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">=</span><span style="display:inline-block;width:0.28em"></span><span class="ML__left-right" style="margin-top:-0.8804399999999999em;height:2.02488em"><span class="ML__open ML__delim-size3">{</span><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.15em"><span class="ML__center" style="top:-3.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.84em;display:inline-block"><span class="ML__cmr">−</span><span class="ML__mathit">x</span><span class="ML__cmr">,</span><span style="display:inline-block;width:0.17em"></span><span class="ML__mathit">x</span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr"><</span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">0</span></span></span><span class="ML__center" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.84em;display:inline-block"><span class="ML__enspace"></span><span class="ML__mathit">x</span><span class="ML__cmr">,</span><span style="display:inline-block;width:0.17em"></span><span class="ML__mathit">x</span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">≥</span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">0</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.89em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span>',
  '\\dot{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.94em"></span><span class="ML__strut--bottom" style="height:1.14em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.94em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.26em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__accent-body" style="height:0.67em;display:inline-block">˙</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\ddot{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.94em"></span><span class="ML__strut--bottom" style="height:1.14em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.94em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.26em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__accent-body" style="height:0.67em;display:inline-block">¨</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\mathring{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.97em"></span><span class="ML__strut--bottom" style="height:1.17em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.97em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.26em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__accent-body" style="height:0.7em;display:inline-block">˚</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\hat{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.97em"></span><span class="ML__strut--bottom" style="height:1.17em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.97em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.26em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__accent-body" style="height:0.7em;display:inline-block">^</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\check{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.9em"></span><span class="ML__strut--bottom" style="height:1.1em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.9em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.26em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__accent-body" style="height:0.63em;display:inline-block">ˇ</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\acute{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.97em"></span><span class="ML__strut--bottom" style="height:1.17em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.97em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.26em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__accent-body" style="height:0.7em;display:inline-block">ˊ</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\grave{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.97em"></span><span class="ML__strut--bottom" style="height:1.17em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.97em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.26em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__accent-body" style="height:0.7em;display:inline-block">ˋ</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\breve{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.97em"></span><span class="ML__strut--bottom" style="height:1.17em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.97em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.26em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__accent-body" style="height:0.7em;display:inline-block">˘</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\widetilde{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1.28em"></span><span class="ML__strut--bottom" style="height:1.48em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.28em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.98em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.26em;display:inline-block"><span style="display:inline-block;height:0.13em;min-width:0"><span class="ML__stretchy" style="height:0.26em"><svg width="100%" height="0.26em" viewBox="0 0 600 260" preserveAspectRatio="none" ><path fill="currentcolor" d="M200 55.538c-77 0-168 73.953-177 73.953-3 0-7\n-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0\n 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0\n 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128\n-68.267.847-113-73.952-191-73.952z"></path></svg></span></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\bar{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.84em"></span><span class="ML__strut--bottom" style="height:1.04em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.84em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.26em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__accent-body" style="height:0.57em;display:inline-block">ˉ</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\bar{\\bar{#0}}': '<span class="ML__latex"><span class="ML__strut" style="height:0.98em"></span><span class="ML__strut--bottom" style="height:1.18em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.98em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:1.04em;display:inline-block"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.84em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.26em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__accent-body" style="height:0.57em;display:inline-block">ˉ</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span><span class="ML__center" style="top:-3.4em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__accent-body" style="height:0.57em;display:inline-block">ˉ</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\vec{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.99em"></span><span class="ML__strut--bottom" style="height:1.19em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.99em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.26em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__accent-body ML__accent-vec" style="height:0.72em;display:inline-block">⃗</span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\overbrace{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1.45em"></span><span class="ML__strut--bottom" style="height:1.65em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.45em"><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span><span class="ML__center" style="top:-3.9em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.55em;display:inline-block"><span style="display:inline-block;height:0.548em;min-width:1.6em;"><span class="slice-1-of-3" style=height:0.548em><svg width=400em height=0.548em viewBox="0 0 400000 548" preserveAspectRatio="xMinYMin slice"><path fill="currentcolor" d="M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117\n-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7\n 5-6 9-10 13-.7 1-7.3 1-20 1H6z"></path></svg></span><span class="slice-2-of-3" style=height:0.548em><svg width=400em height=0.548em viewBox="0 0 400000 548" preserveAspectRatio="xMidYMin slice"><path fill="currentcolor" d="M200428 334\nc-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14\n-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7\n 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11\n 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z"></path></svg></span><span class="slice-3-of-3" style=height:0.548em><svg width=400em height=0.548em viewBox="0 0 400000 548" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M400000 542l\n-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5\ns-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1\nc124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z"></path></svg></span></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\underbrace{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:1.55em;vertical-align:-0.84em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.7em"><span class="ML__center" style="top:-2.36em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.55em;display:inline-block"><span style="display:inline-block;height:0.548em;min-width:1.6em;"><span class="slice-1-of-3" style=height:0.548em><svg width=400em height=0.548em viewBox="0 0 400000 548" preserveAspectRatio="xMinYMin slice"><path fill="currentcolor" d="M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13\n 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688\n 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7\n-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z"></path></svg></span><span class="slice-2-of-3" style=height:0.548em><svg width=400em height=0.548em viewBox="0 0 400000 548" preserveAspectRatio="xMidYMin slice"><path fill="currentcolor" d="M199572 214\nc100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14\n 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3\n 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0\n-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z"></path></svg></span><span class="slice-3-of-3" style=height:0.548em><svg width=400em height=0.548em viewBox="0 0 400000 548" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3\n 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237\n-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z"></path></svg></span></span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.85em"></span></span></span></span></span>',
  '\\boxed{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1em"></span><span class="ML__strut--bottom" style="height:1.5em;vertical-align:-0.5em"></span><span class="ML__base"><span style="display:inline-block;position:relative;line-height:0;padding-left:0.3em;padding-right:0.3em;height:1.5em;margin-top:-0.3em;top:0.1em;vertical-align:0.8em"><span class="ML__box" style="box-sizing:border-box;position:absolute;top:0;left:0;height:1.5em;width:100%;border:0.04em solid #000000"></span><span style="display:inline-block;position:relative;height:0.7em;vertical-align:-0.7em"><span class="ML__cmr">▢</span></span></span></span></span>',
  '\\boxed{a^2+b^2=c^2}': '<span class="ML__latex"><span class="ML__strut" style="height:1.17em"></span><span class="ML__strut--bottom" style="height:1.55em;vertical-align:-0.38em"></span><span class="ML__base"><span style="display:inline-block;position:relative;line-height:0;padding-left:0.3em;padding-right:0.3em;height:1.54em;margin-top:-0.3em;top:-0.19em;vertical-align:0.68em"><span class="ML__box" style="box-sizing:border-box;position:absolute;top:0;left:0;height:1.55em;width:100%;border:0.04em solid #000000"></span><span style="display:inline-block;position:relative;height:0.86em;vertical-align:-0.86em"><span class="ML__mathit">a</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span></span></span></span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__mathit">b</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span></span></span></span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">=</span><span style="display:inline-block;width:0.28em"></span><span class="ML__mathit">c</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span></span></span></span></span></span></span></span>',
  '\\overline{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.9em"></span><span class="ML__strut--bottom" style="height:1.11em;vertical-align:-0.2em"></span><span class="ML__base"><span class="overline"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.9em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span style="top:-3.82em"><span class="ML__pstrut" style="height:3em"></span><span class="overline-line" style="height:0.04em;display:inline-block"></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span></span>',
  '\\underline{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:1.1em;vertical-align:-0.4em"></span><span class="ML__base"><span class="underline"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.7em"><span style="top:-2.64em"><span class="ML__pstrut" style="height:3em"></span><span class="underline-line" style="height:0.04em;display:inline-block"></span></span><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.4em"></span></span></span></span></span></span>',
  '\\overline{A}': '<span class="ML__latex"><span class="ML__strut" style="height:0.89em"></span><span class="ML__base"><span class="overline"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.89em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.69em;display:inline-block"><span class="ML__mathit">A</span></span></span><span style="top:-3.8em"><span class="ML__pstrut" style="height:3em"></span><span class="overline-line" style="height:0.04em;display:inline-block"></span></span></span></span></span></span></span></span>',
  '\\overline{ABC}': '<span class="ML__latex"><span class="ML__strut" style="height:0.89em"></span><span class="ML__base"><span class="overline"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.89em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.69em;display:inline-block"><span class="ML__mathit">A</span><span class="ML__mathit" style="margin-right:0.06em">B</span><span class="ML__mathit" style="margin-right:0.08em">C</span></span></span><span style="top:-3.8em"><span class="ML__pstrut" style="height:3em"></span><span class="overline-line" style="height:0.04em;display:inline-block"></span></span></span></span></span></span></span></span>',
  '\\overline{x\\oplus y}': '<span class="ML__latex"><span class="ML__strut" style="height:0.79em"></span><span class="ML__strut--bottom" style="height:0.98em;vertical-align:-0.19em"></span><span class="ML__base"><span class="overline"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.79em"><span style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.78em;display:inline-block"><span class="ML__mathit">x</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">⊕</span><span style="display:inline-block;width:0.23em"></span><span class="ML__mathit" style="margin-right:0.04em">y</span></span></span><span style="top:-3.7em"><span class="ML__pstrut" style="height:3em"></span><span class="overline-line" style="height:0.04em;display:inline-block"></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span></span>',
  '\\colon=': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__base"><span class="ML__cmr">:</span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">=</span></span></span>',
  '==': '<span class="ML__latex"><span class="ML__strut" style="height:0.37em"></span><span class="ML__strut--bottom" style="height:0.24em;vertical-align:0.14em"></span><span class="ML__base"><span class="ML__cmr">==</span></span></span>',
  '+=': '<span class="ML__latex"><span class="ML__strut" style="height:0.59em"></span><span class="ML__strut--bottom" style="height:0.67em;vertical-align:-0.08em"></span><span class="ML__base"><span class="ML__cmr">+</span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">=</span></span></span>',
  '-=': '<span class="ML__latex"><span class="ML__strut" style="height:0.59em"></span><span class="ML__strut--bottom" style="height:0.67em;vertical-align:-0.08em"></span><span class="ML__base"><span class="ML__cmr">−</span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">=</span></span></span>',
  '\\measeq': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__cmr">≝</span></span></span>',
  '\\eqdef': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__cmr">≞</span></span></span>',
  '\\overleftarrow{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1.43em"></span><span class="ML__strut--bottom" style="height:1.63em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.43em"><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span><span class="ML__center" style="top:-3.89em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span style="display:inline-block;height:0.522em;min-width:0.888em;"><span class="slice-1-of-1" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMinYMin slice"><path fill="currentcolor" d="M400000 241H110l3-3c68.7-52.7 113.7-120\n 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8\n-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247\nc-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208\n 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3\n 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202\n l-3-3h399890zM100 241v40h399900v-40z"></path></svg></span></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\overrightarrow{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1.43em"></span><span class="ML__strut--bottom" style="height:1.63em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.43em"><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span><span class="ML__center" style="top:-3.89em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span style="display:inline-block;height:0.522em;min-width:0.888em;"><span class="slice-1-of-1" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z"></path></svg></span></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\underleftarrow{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:1.53em;vertical-align:-0.82em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.7em"><span class="ML__center" style="top:-2.37em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span style="display:inline-block;height:0.522em;min-width:0.888em;"><span class="slice-1-of-1" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMinYMin slice"><path fill="currentcolor" d="M400000 241H110l3-3c68.7-52.7 113.7-120\n 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8\n-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247\nc-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208\n 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3\n 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202\n l-3-3h399890zM100 241v40h399900v-40z"></path></svg></span></span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.83em"></span></span></span></span></span>',
  '\\underrightarrow{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:1.53em;vertical-align:-0.82em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.7em"><span class="ML__center" style="top:-2.37em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span style="display:inline-block;height:0.522em;min-width:0.888em;"><span class="slice-1-of-1" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z"></path></svg></span></span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.83em"></span></span></span></span></span>',
  '\\overleftrightarrow{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1.43em"></span><span class="ML__strut--bottom" style="height:1.63em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.43em"><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span><span class="ML__center" style="top:-3.89em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span style="display:inline-block;height:0.522em;min-width:0.888em;"><span class="slice-1-of-2" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMinYMin slice"><path fill="currentcolor" d="M400000 241H110l3-3c68.7-52.7 113.7-120\n 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8\n-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247\nc-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208\n 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3\n 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202\n l-3-3h399890zM100 241v40h399900v-40z"></path></svg></span><span class="slice-2-of-2" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z"></path></svg></span></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\underleftrightarrow{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:1.53em;vertical-align:-0.82em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.7em"><span class="ML__center" style="top:-2.37em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span style="display:inline-block;height:0.522em;min-width:0.888em;"><span class="slice-1-of-2" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMinYMin slice"><path fill="currentcolor" d="M400000 241H110l3-3c68.7-52.7 113.7-120\n 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8\n-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247\nc-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208\n 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3\n 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202\n l-3-3h399890zM100 241v40h399900v-40z"></path></svg></span><span class="slice-2-of-2" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z"></path></svg></span></span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.83em"></span></span></span></span></span>',
  '\\Overrightarrow{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1.46em"></span><span class="ML__strut--bottom" style="height:1.66em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.46em"><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span><span class="ML__center" style="top:-3.91em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.57em;display:inline-block"><span style="display:inline-block;height:0.56em;min-width:0.888em;"><span class="slice-1-of-1" style=height:0.56em><svg width=400em height=0.56em viewBox="0 0 400000 560" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M399738 392l\n-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5\n 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88\n-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68\n-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18\n-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782\nc-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3\n-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z"></path></svg></span></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\overleftharpoon{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1.43em"></span><span class="ML__strut--bottom" style="height:1.63em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.43em"><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span><span class="ML__center" style="top:-3.89em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span style="display:inline-block;height:0.522em;min-width:0.888em;"><span class="slice-1-of-1" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMinYMin slice"><path fill="currentcolor" d="M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3\n-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5\n-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7\n-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z"></path></svg></span></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\overrightharpoon{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1.43em"></span><span class="ML__strut--bottom" style="height:1.63em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.43em"><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span><span class="ML__center" style="top:-3.89em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span style="display:inline-block;height:0.522em;min-width:0.888em;"><span class="slice-1-of-1" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3\n-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2\n-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58\n 69.2 92 94.5zm0 0v40h399900v-40z"></path></svg></span></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\overlinesegment{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1.43em"></span><span class="ML__strut--bottom" style="height:1.63em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.43em"><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span><span class="ML__center" style="top:-3.89em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span style="display:inline-block;height:0.522em;min-width:0.888em;"><span class="slice-1-of-2" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMinYMin slice"><path fill="currentcolor" d="M40 281 V428 H0 V94 H40 V241 H400000 v40z\nM40 281 V428 H0 V94 H40 V241 H400000 v40z"></path></svg></span><span class="slice-2-of-2" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M399960 241 V94 h40 V428 h-40 V281 H0 v-40z\nM399960 241 V94 h40 V428 h-40 V281 H0 v-40z"></path></svg></span></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\underlinesegment{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:1.53em;vertical-align:-0.82em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.7em"><span class="ML__center" style="top:-2.37em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span style="display:inline-block;height:0.522em;min-width:0.888em;"><span class="slice-1-of-2" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMinYMin slice"><path fill="currentcolor" d="M40 281 V428 H0 V94 H40 V241 H400000 v40z\nM40 281 V428 H0 V94 H40 V241 H400000 v40z"></path></svg></span><span class="slice-2-of-2" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M399960 241 V94 h40 V428 h-40 V281 H0 v-40z\nM399960 241 V94 h40 V428 h-40 V281 H0 v-40z"></path></svg></span></span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.83em"></span></span></span></span></span>',
  '\\overgroup{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1.25em"></span><span class="ML__strut--bottom" style="height:1.45em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.25em"><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">▢</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span><span class="ML__center" style="top:-3.8em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.35em;display:inline-block"><span style="display:inline-block;height:0.342em;min-width:0.888em;"><span class="slice-1-of-2" style=height:0.342em><svg width=400em height=0.342em viewBox="0 0 400000 342" preserveAspectRatio="xMinYMin slice"><path fill="currentcolor" d="M400000 80\nH435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0\n 435 0h399565z"></path></svg></span><span class="slice-2-of-2" style=height:0.342em><svg width=400em height=0.342em viewBox="0 0 400000 342" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0\n 3-1 3-3v-38c-76-158-257-219-435-219H0z"></path></svg></span></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '\\underrightarrow{yields}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:1.52em;vertical-align:-0.81em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.7em"><span class="ML__center" style="top:-2.37em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span style="display:inline-block;height:0.522em;min-width:0.888em;"><span class="slice-1-of-1" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z"></path></svg></span></span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.89em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__mathit" style="margin-right:0.04em">y</span><span class="ML__mathit">ie</span><span class="ML__mathit" style="margin-right:0.02em">l</span><span class="ML__mathit">ds</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.82em"></span></span></span></span></span>',
  '\\underrightarrow{∆}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:1.53em;vertical-align:-0.82em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.7em"><span class="ML__center" style="top:-2.37em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span style="display:inline-block;height:0.522em;min-width:0.888em;"><span class="slice-1-of-1" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z"></path></svg></span></span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__cmr">∆</span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.83em"></span></span></span></span></span>',
  '\\pm': '<span class="ML__latex"><span class="ML__strut" style="height:0.59em"></span><span class="ML__strut--bottom" style="height:0.67em;vertical-align:-0.08em"></span><span class="ML__base"><span class="ML__cmr">±</span></span></span>',
  '\\infty': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__base"><span class="ML__cmr">∞</span></span></span>',
  '=': '<span class="ML__latex"><span class="ML__strut" style="height:0.37em"></span><span class="ML__strut--bottom" style="height:0.24em;vertical-align:0.14em"></span><span class="ML__base"><span class="ML__cmr">=</span></span></span>',
  '\\neq': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.89em;vertical-align:-0.19em"></span><span class="ML__base"><span class="ML__rlap"><span class="ML__inner"><span class="ML__cmr"></span></span><span class="ML__fix"></span></span><span class="ML__cmr">=</span></span></span>',
  '\\thicksim': '<span class="ML__latex"><span class="ML__strut" style="height:0.37em"></span><span class="ML__strut--bottom" style="height:0.24em;vertical-align:0.14em"></span><span class="ML__base"><span class="ML__ams">∼</span></span></span>',
  '\\times': '<span class="ML__latex"><span class="ML__strut" style="height:0.59em"></span><span class="ML__strut--bottom" style="height:0.67em;vertical-align:-0.08em"></span><span class="ML__base"><span class="ML__cmr">×</span></span></span>',
  '\\div': '<span class="ML__latex"><span class="ML__strut" style="height:0.59em"></span><span class="ML__strut--bottom" style="height:0.67em;vertical-align:-0.08em"></span><span class="ML__base"><span class="ML__cmr">÷</span></span></span>',
  '!': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__base"><span class="ML__cmr">!</span></span></span>',
  '\\propto': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__base"><span class="ML__cmr">∝</span></span></span>',
  '<': '<span class="ML__latex"><span class="ML__strut" style="height:0.54em"></span><span class="ML__strut--bottom" style="height:0.58em;vertical-align:-0.03em"></span><span class="ML__base"><span class="ML__cmr"><</span></span></span>',
  '\\ll': '<span class="ML__latex"><span class="ML__strut" style="height:0.54em"></span><span class="ML__strut--bottom" style="height:0.58em;vertical-align:-0.03em"></span><span class="ML__base"><span class="ML__cmr">≪</span></span></span>',
  '>': '<span class="ML__latex"><span class="ML__strut" style="height:0.54em"></span><span class="ML__strut--bottom" style="height:0.58em;vertical-align:-0.03em"></span><span class="ML__base"><span class="ML__cmr">></span></span></span>',
  '\\gg': '<span class="ML__latex"><span class="ML__strut" style="height:0.54em"></span><span class="ML__strut--bottom" style="height:0.58em;vertical-align:-0.03em"></span><span class="ML__base"><span class="ML__cmr">≫</span></span></span>',
  '\\le': '<span class="ML__latex"><span class="ML__strut" style="height:0.64em"></span><span class="ML__strut--bottom" style="height:0.78em;vertical-align:-0.13em"></span><span class="ML__base"><span class="ML__cmr">≤</span></span></span>',
  '\\geq': '<span class="ML__latex"><span class="ML__strut" style="height:0.64em"></span><span class="ML__strut--bottom" style="height:0.78em;vertical-align:-0.13em"></span><span class="ML__base"><span class="ML__cmr">≥</span></span></span>',
  '\\mp': '<span class="ML__latex"><span class="ML__strut" style="height:0.59em"></span><span class="ML__strut--bottom" style="height:0.67em;vertical-align:-0.08em"></span><span class="ML__base"><span class="ML__cmr">∓</span></span></span>',
  '\\cong': '<span class="ML__latex"><span class="ML__strut" style="height:0.59em"></span><span class="ML__strut--bottom" style="height:0.57em;vertical-align:0.03em"></span><span class="ML__base"><span class="ML__cmr">≅</span></span></span>',
  '\\approx': '<span class="ML__latex"><span class="ML__strut" style="height:0.49em"></span><span class="ML__strut--bottom" style="height:0.47em;vertical-align:0.02em"></span><span class="ML__base"><span class="ML__cmr">≈</span></span></span>',
  '\\equiv': '<span class="ML__latex"><span class="ML__strut" style="height:0.47em"></span><span class="ML__strut--bottom" style="height:0.43em;vertical-align:0.04em"></span><span class="ML__base"><span class="ML__cmr">≡</span></span></span>',
  '\\forall': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__base"><span class="ML__cmr">∀</span></span></span>',
  '\\complement': '<span class="ML__latex"><span class="ML__strut" style="height:0.83em"></span><span class="ML__base"><span class="ML__ams">∁</span></span></span>',
  '\\partial': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__base"><span class="ML__cmr" style="margin-right:0.06em">∂</span></span></span>',
  '\\sqrt': '<span class="ML__latex"><span class="ML__strut" style="height:0.66em"></span><span class="ML__strut--bottom" style="height:1.21em;vertical-align:-0.54em"></span><span class="ML__base"><span style="display:inline-block;height:1.21em"><span class="ML__sqrt-sign" style="top:0.2em"><span class="ML__delim-size1">√</span></span><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.66em"><span style="top:-2.04em"><span class="ML__pstrut" style="height:2.04em"></span><span style="height:0;display:inline-block"></span></span><span style="top:-2.61em"><span class="ML__pstrut" style="height:2.04em"></span><span class="ML__sqrt-line" style="height:0.04em;display:inline-block"></span></span></span></span></span></span></span></span>',
  '\\cup': '<span class="ML__latex"><span class="ML__strut" style="height:0.56em"></span><span class="ML__base"><span class="ML__cmr">∪</span></span></span>',
  '\\cap': '<span class="ML__latex"><span class="ML__strut" style="height:0.56em"></span><span class="ML__base"><span class="ML__cmr">∩</span></span></span>',
  '\\emptyset': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:0.81em;vertical-align:-0.05em"></span><span class="ML__base"><span class="ML__cmr">∅</span></span></span>',
  '\\%': '<span class="ML__latex"><span class="ML__strut" style="height:0.75em"></span><span class="ML__strut--bottom" style="height:0.81em;vertical-align:-0.05em"></span><span class="ML__base"><span class="ML__cmr">%</span></span></span>',
  '°': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__base"><span class="ML__cmr">°</span></span></span>',
  '°F': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__base"><span class="ML__cmr">°</span><span class="ML__mathit" style="margin-right:0.14em">F</span></span></span>',
  '°C': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__base"><span class="ML__cmr">°</span><span class="ML__mathit" style="margin-right:0.08em">C</span></span></span>',
  '∆': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__cmr">∆</span></span></span>',
  '\\nabla': '<span class="ML__latex"><span class="ML__strut" style="height:0.69em"></span><span class="ML__base"><span class="ML__cmr">∇</span></span></span>',
  '\\exists': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__base"><span class="ML__cmr">∃</span></span></span>',
  '\\nexists': '<span class="ML__latex"><span class="ML__strut" style="height:0.69em"></span><span class="ML__base"><span class="ML__ams">∄</span></span></span>',
  '\\in': '<span class="ML__latex"><span class="ML__strut" style="height:0.54em"></span><span class="ML__strut--bottom" style="height:0.58em;vertical-align:-0.03em"></span><span class="ML__base"><span class="ML__cmr">∈</span></span></span>',
  '\\ni': '<span class="ML__latex"><span class="ML__strut" style="height:0.54em"></span><span class="ML__strut--bottom" style="height:0.58em;vertical-align:-0.03em"></span><span class="ML__base"><span class="ML__cmr">∋</span></span></span>',
  '\\gets': '<span class="ML__latex"><span class="ML__strut" style="height:0.37em"></span><span class="ML__strut--bottom" style="height:0.24em;vertical-align:0.14em"></span><span class="ML__base"><span class="ML__cmr">←</span></span></span>',
  '\\uparrow': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.89em;vertical-align:-0.19em"></span><span class="ML__base"><span class="ML__cmr">↑</span></span></span>',
  '\\rightarrow': '<span class="ML__latex"><span class="ML__strut" style="height:0.37em"></span><span class="ML__strut--bottom" style="height:0.24em;vertical-align:0.14em"></span><span class="ML__base"><span class="ML__cmr">→</span></span></span>',
  '\\downarrow': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.89em;vertical-align:-0.19em"></span><span class="ML__base"><span class="ML__cmr">↓</span></span></span>',
  '\\leftrightarrow': '<span class="ML__latex"><span class="ML__strut" style="height:0.37em"></span><span class="ML__strut--bottom" style="height:0.24em;vertical-align:0.14em"></span><span class="ML__base"><span class="ML__cmr">↔</span></span></span>',
  '\\therefore': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__base"><span class="ML__ams">∴</span></span></span>',
  '+': '<span class="ML__latex"><span class="ML__strut" style="height:0.59em"></span><span class="ML__strut--bottom" style="height:0.67em;vertical-align:-0.08em"></span><span class="ML__base"><span class="ML__cmr">+</span></span></span>',
  '-': '<span class="ML__latex"><span class="ML__strut" style="height:0.59em"></span><span class="ML__strut--bottom" style="height:0.67em;vertical-align:-0.08em"></span><span class="ML__base"><span class="ML__cmr">−</span></span></span>',
  '\\lnot': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__ams">¬</span></span></span>',
  '\\alpha': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__base"><span class="lcGreek ML__mathit" style="margin-right:0.01em">α</span></span></span>',
  '\\beta': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.89em;vertical-align:-0.19em"></span><span class="ML__base"><span class="lcGreek ML__mathit" style="margin-right:0.06em">β</span></span></span>',
  '\\gamma': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__strut--bottom" style="height:0.63em;vertical-align:-0.19em"></span><span class="ML__base"><span class="lcGreek ML__mathit" style="margin-right:0.06em">γ</span></span></span>',
  '\\delta': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__base"><span class="lcGreek ML__mathit" style="margin-right:0.04em">δ</span></span></span>',
  '\\varepsilon': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__base"><span class="lcGreek ML__mathit">ε</span></span></span>',
  '\\epsilon': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__cmr">ϵ</span></span></span>',
  '\\theta': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__base"><span class="lcGreek ML__mathit" style="margin-right:0.03em">θ</span></span></span>',
  '\\vartheta': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__base"><span class="lcGreek ML__mathit">ϑ</span></span></span>',
  '\\mu': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__strut--bottom" style="height:0.63em;vertical-align:-0.19em"></span><span class="ML__base"><span class="lcGreek ML__mathit">μ</span></span></span>',
  '\\pi': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__base"><span class="lcGreek ML__mathit" style="margin-right:0.04em">π</span></span></span>',
  '\\rho': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__strut--bottom" style="height:0.63em;vertical-align:-0.19em"></span><span class="ML__base"><span class="lcGreek ML__mathit">ρ</span></span></span>',
  '\\sigma': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__base"><span class="lcGreek ML__mathit" style="margin-right:0.04em">σ</span></span></span>',
  '\\tau': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__base"><span class="lcGreek ML__mathit" style="margin-right:0.12em">τ</span></span></span>',
  '\\varphi': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__strut--bottom" style="height:0.63em;vertical-align:-0.19em"></span><span class="ML__base"><span class="lcGreek ML__mathit">φ</span></span></span>',
  '\\omega': '<span class="ML__latex"><span class="ML__strut" style="height:0.44em"></span><span class="ML__base"><span class="lcGreek ML__mathit" style="margin-right:0.04em">ω</span></span></span>',
  '\\ast': '<span class="ML__latex"><span class="ML__strut" style="height:0.47em"></span><span class="ML__strut--bottom" style="height:0.44em;vertical-align:0.04em"></span><span class="ML__base"><span class="ML__cmr">∗</span></span></span>',
  '\\bullet': '<span class="ML__latex"><span class="ML__strut" style="height:0.45em"></span><span class="ML__strut--bottom" style="height:0.39em;vertical-align:0.06em"></span><span class="ML__base"><span class="ML__cmr">∙</span></span></span>',
  '⋰': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__cmr">⋰</span></span></span>',
  '\\aleph': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__base"><span class="ML__cmr">ℵ</span></span></span>',
  '\\beth': '<span class="ML__latex"><span class="ML__strut" style="height:0.69em"></span><span class="ML__base"><span class="ML__ams">ℶ</span></span></span>',
  '\\blacksquare': '<span class="ML__latex"><span class="ML__strut" style="height:0.68em"></span><span class="ML__base"><span class="ML__ams">■</span></span></span>',
  '\\ce{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1em"></span><span class="ML__strut--bottom" style="height:1.5em;vertical-align:-0.5em"></span><span class="ML__base"><span style="display:inline-block;position:relative;line-height:0;padding-left:0.3em;padding-right:0.3em;height:1.5em;margin-top:-0.3em;top:0.1em;vertical-align:0.8em"><span class="ML__box" style="box-sizing:border-box;position:absolute;top:0;left:0;height:1.5em;width:100%;border:0.04em solid #000000"></span><span style="display:inline-block;position:relative;height:0.7em;vertical-align:-0.7em"><span class="ML__cmr">▢</span></span></span></span></span>',
  '\\overset{#0}{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:1.53em"></span><span class="ML__strut--bottom" style="height:1.73em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.53em"><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.9em;display:inline-block"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3.94em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.2em"></span></span></span></span></span>',
  '#0_{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.99em;vertical-align:-0.28em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.34em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.29em"></span></span></span></span></span></span>',
  '#0^{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.91em"></span><span class="ML__strut--bottom" style="height:1.11em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.91em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span></span></span></span></span></span>',
  '#0_{#0}#0': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.99em;vertical-align:-0.28em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.34em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.29em"></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '#0_{#0}#0_{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.99em;vertical-align:-0.28em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.34em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.29em"></span></span></span></span><span class="ML__cmr">▢</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.34em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.29em"></span></span></span></span></span></span>',
  '#0_{#0}^{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.98em"></span><span class="ML__strut--bottom" style="height:1.42em;vertical-align:-0.44em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.98em"><span style="top:-2.69em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.48em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.45em"></span></span></span></span></span></span>',
  '[#0_{#0}]^{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.97em"></span><span class="ML__strut--bottom" style="height:1.26em;vertical-align:-0.28em"></span><span class="ML__base"><span class="ML__cmr">[▢</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.34em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.29em"></span></span></span></span><span class="ML__cmr">]</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.97em"><span style="top:-3.47em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span></span></span></span></span></span>',
  '#0^{#0}#0': '<span class="ML__latex"><span class="ML__strut" style="height:0.91em"></span><span class="ML__strut--bottom" style="height:1.11em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.91em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '^{#0}_{#0}#0^{#0}': '<span class="ML__latex"><span class="ML__strut" style="height:0.98em"></span><span class="ML__strut--bottom" style="height:1.42em;vertical-align:-0.44em"></span><span class="ML__base"><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.98em"><span style="top:-2.69em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.48em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.45em"></span></span></span></span><span class="ML__cmr">▢</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.91em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span></span></span></span></span></span>',
  '#0 #0_{#0}#0': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.99em;vertical-align:-0.28em"></span><span class="ML__base"><span class="ML__cmr">▢▢</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.34em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.29em"></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\tfrac{#0}{#0} #0_{#0}#0': '<span class="ML__latex"><span class="ML__strut" style="height:0.94em"></span><span class="ML__strut--bottom" style="height:1.43em;vertical-align:-0.48em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.94em"><span class="ML__center" style="top:-2.65em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.45em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.49em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__cmr">▢</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.34em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.29em"></span></span></span></span><span class="ML__cmr">▢</span></span></span>',
  '\\ #0 \\ = \\ #0 \\ + \\ #0': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.9em;vertical-align:-0.2em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">=</span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">▢</span></span></span>',
  '#0 \\ + \\ #0 \\ \\xlongequal[#0]{#0} \\ #0 \\ + \\ #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.17em"></span><span class="ML__strut--bottom" style="height:1.9em;vertical-align:-0.73em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.28em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.17em"><span class="ML__center ML__label_padding" style="top:-2.5em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-2.99em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.34em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span><span style="display:inline-block;height:0.334em;min-width:0.888em;"><span class="slice-1-of-1" style=height:0.334em><svg width=400em height=0.334em viewBox="0 0 400000 334" preserveAspectRatio="xMinYMin slice"><path fill="currentcolor" d="M0 50 h400000 v40H0z m0 194h40000v40H0z\nM0 50 h400000 v40H0z m0 194h40000v40H0z"></path></svg></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span><span class="ML__center ML__label_padding" style="top:-3.57em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.74em"></span></span></span><span class="ML__cmr"></span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">▢</span></span></span>',
  '#0 \\ + \\ #0 \\ \\longrightarrow[#0]{#0} \\ #0 \\ + \\ #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.26em"></span><span class="ML__strut--bottom" style="height:2.09em;vertical-align:-0.82em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.28em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.26em"><span class="ML__center ML__label_padding" style="top:-2.41em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span><span style="display:inline-block;height:0.522em;min-width:1.469em;"><span class="slice-1-of-1" style=height:0.522em><svg width=400em height=0.522em viewBox="0 0 400000 522" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z"></path></svg></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span><span class="ML__center ML__label_padding" style="top:-3.66em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.83em"></span></span></span><span class="ML__cmr"></span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">▢</span></span></span>',
  '#0 \\ + \\ #0 \\ \\xtofrom[#0]{#0} \\ #0 \\ + \\ #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.26em"></span><span class="ML__strut--bottom" style="height:2.09em;vertical-align:-0.82em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.28em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.26em"><span class="ML__center ML__label_padding" style="top:-2.41em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.53em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span><span style="display:inline-block;height:0.528em;min-width:1.75em;"><span class="slice-1-of-2" style=height:0.528em><svg width=400em height=0.528em viewBox="0 0 400000 528" preserveAspectRatio="xMinYMin slice"><path fill="currentcolor" d="M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23\n-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8\nc28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3\n 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z"></path></svg></span><span class="slice-2-of-2" style=height:0.528em><svg width=400em height=0.528em viewBox="0 0 400000 528" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23\n 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32\n-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142\n-167z M100 147v40h399900v-40zM0 341v40h399900v-40z"></path></svg></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span><span class="ML__center ML__label_padding" style="top:-3.67em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.83em"></span></span></span><span class="ML__cmr"></span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">▢</span></span></span>',
  '#0 \\ + \\ #0 \\ \\xrightleftharpoons[#0]{#0} \\ #0 \\ + \\ #0': '<span class="ML__latex"><span class="ML__strut" style="height:1.36em"></span><span class="ML__strut--bottom" style="height:2.28em;vertical-align:-0.92em"></span><span class="ML__base"><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.28em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.36em"><span class="ML__center ML__label_padding" style="top:-2.31em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.72em;display:inline-block"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span><span style="display:inline-block;height:0.716em;min-width:1.75em;"><span class="slice-1-of-2" style=height:0.716em><svg width=400em height=0.716em viewBox="0 0 400000 716" preserveAspectRatio="xMinYMin slice"><path fill="currentcolor" d="M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12\n 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7\n-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0\nv40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z"></path></svg></span><span class="slice-2-of-2" style=height:0.716em><svg width=400em height=0.716em viewBox="0 0 400000 716" preserveAspectRatio="xMaxYMin slice"><path fill="currentcolor" d="M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11\n-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7\n 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z\nm0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z"></path></svg></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span></span><span class="ML__center ML__label_padding" style="top:-3.76em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.63em;display:inline-block;font-size: 70%"><span class="ML__cmr">▢</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.93em"></span></span></span><span class="ML__cmr"></span><span style="display:inline-block;width:0.28em"></span><span class="ML__cmr">▢</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">+</span><span style="display:inline-block;width:0.23em"></span><span class="ML__cmr">▢</span></span></span>',
  '\\overset{+4}{Mn}': '<span class="ML__latex"><span class="ML__strut" style="height:1.4em"></span><span class="ML__base"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:1.4em"><span class="ML__center" style="top:-3em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.69em;display:inline-block"><span class="ML__mathit" style="margin-right:0.11em">M</span><span class="ML__mathit">n</span></span></span><span class="ML__center" style="top:-3.84em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.51em;display:inline-block;font-size: 70%"><span class="ML__cmr">+4</span></span></span></span></span></span></span></span>',
  'MnO_{2}': '<span class="ML__latex"><span class="ML__strut" style="height:0.69em"></span><span class="ML__strut--bottom" style="height:0.84em;vertical-align:-0.15em"></span><span class="ML__base"><span class="ML__mathit" style="margin-right:0.11em">M</span><span class="ML__mathit">n</span><span class="ML__mathit" style="margin-right:0.03em">O</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.31em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.15em"></span></span></span></span></span></span>',
  'H_{2}O': '<span class="ML__latex"><span class="ML__strut" style="height:0.69em"></span><span class="ML__strut--bottom" style="height:0.84em;vertical-align:-0.15em"></span><span class="ML__base"><span class="ML__mathit" style="margin-right:0.09em">H</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.31em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.15em"></span></span></span></span><span class="ML__mathit" style="margin-right:0.03em">O</span></span></span>',
  'Sb_{2}O_{3}': '<span class="ML__latex"><span class="ML__strut" style="height:0.7em"></span><span class="ML__strut--bottom" style="height:0.85em;vertical-align:-0.15em"></span><span class="ML__base"><span class="ML__mathit" style="margin-right:0.06em">S</span><span class="ML__mathit">b</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.31em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.15em"></span></span></span></span><span class="ML__mathit" style="margin-right:0.03em">O</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.31em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">3</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.15em"></span></span></span></span></span></span>',
  'Y^{99+}': '<span class="ML__latex"><span class="ML__strut" style="height:0.87em"></span><span class="ML__base"><span class="ML__mathit" style="margin-right:0.23em">Y</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.51em;display:inline-block;font-size: 70%"><span class="ML__cmr">99+</span></span></span></span></span></span></span></span></span>',
  '[AgCl_{2}]^{-}': '<span class="ML__latex"><span class="ML__strut" style="height:0.89em"></span><span class="ML__strut--bottom" style="height:1.14em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__cmr">[</span><span class="ML__mathit">A</span><span class="ML__mathit" style="margin-right:0.04em">g</span><span class="ML__mathit" style="margin-right:0.08em">C</span><span class="ML__mathit" style="margin-right:0.02em">l</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.31em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.15em"></span></span></span></span><span class="ML__cmr">]</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.89em"><span style="top:-3.47em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.47em;display:inline-block;font-size: 70%"><span class="ML__cmr">−</span></span></span></span></span></span></span></span></span>',
  'CrO_{4}^{2-}': '<span class="ML__latex"><span class="ML__strut" style="height:0.87em"></span><span class="ML__strut--bottom" style="height:1.13em;vertical-align:-0.25em"></span><span class="ML__base"><span class="ML__mathit" style="margin-right:0.08em">C</span><span class="ML__mathit" style="margin-right:0.03em">r</span><span class="ML__mathit" style="margin-right:0.03em">O</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-2.74em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">4</span></span></span><span style="top:-3.41em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.51em;display:inline-block;font-size: 70%"><span class="ML__cmr">2−</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.26em"></span></span></span></span></span></span>',
  'n H_{2}O': '<span class="ML__latex"><span class="ML__strut" style="height:0.69em"></span><span class="ML__strut--bottom" style="height:0.84em;vertical-align:-0.15em"></span><span class="ML__base"><span class="ML__mathit">n</span><span class="ML__mathit" style="margin-right:0.09em">H</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.31em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.15em"></span></span></span></span><span class="ML__mathit" style="margin-right:0.03em">O</span></span></span>',
  '\\tfrac{1}{2} H_{2}O': '<span class="ML__latex"><span class="ML__strut" style="height:0.84em"></span><span class="ML__strut--bottom" style="height:1.18em;vertical-align:-0.34em"></span><span class="ML__base"><span class="ML__mfrac"><span class="ML__nulldelimiter ML__open" style="width:0.12em"></span><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.84em"><span class="ML__center" style="top:-2.65em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span><span style="top:-3.23em"><span class="ML__pstrut" style="height:3em"></span><span class="ML__frac-line" style="height:0.04em;display:inline-block"></span></span><span class="ML__center" style="top:-3.38em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">1</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.35em"></span></span></span><span class="ML__nulldelimiter ML__close" style="width:0.12em"></span></span><span style="display:inline-block;width:0.17em"></span><span class="ML__mathit" style="margin-right:0.09em">H</span><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.31em"><span style="top:-2.85em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">2</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.15em"></span></span></span></span><span class="ML__mathit" style="margin-right:0.03em">O</span></span></span>',
  'H^{3}HO': '<span class="ML__latex"><span class="ML__strut" style="height:0.87em"></span><span class="ML__base"><span class="ML__mathit" style="margin-right:0.09em">H</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">3</span></span></span></span></span></span></span><span class="ML__mathit" style="margin-right:0.09em">H</span><span class="ML__mathit" style="margin-right:0.03em">O</span></span></span>',
  '^{227}_{90}Th^{+}': '<span class="ML__latex"><span class="ML__strut" style="height:0.87em"></span><span class="ML__strut--bottom" style="height:1.12em;vertical-align:-0.24em"></span><span class="ML__base"><span class="ML__msubsup"><span class="ML__vlist-t ML__vlist-t2"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.87em"><span style="top:-2.75em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">90</span></span></span><span style="top:-3.41em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.46em;display:inline-block;font-size: 70%"><span class="ML__cmr">227</span></span></span></span><span class="ML__vlist-s"></span></span><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.25em"></span></span></span></span><span class="ML__mathit" style="margin-right:0.14em">T</span><span class="ML__mathit">h</span><span class="ML__msubsup"><span class="ML__vlist-t"><span class="ML__vlist-r"><span class="ML__vlist" style="height:0.83em"><span style="top:-3.41em;margin-right:0.05em"><span class="ML__pstrut" style="height:3em"></span><span style="height:0.47em;display:inline-block;font-size: 70%"><span class="ML__cmr">+</span></span></span></span></span></span></span></span></span>',
}
